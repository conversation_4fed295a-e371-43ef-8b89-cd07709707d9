package com.yingfei.entity.dto.chart;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.dto.DataSummaryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 抽样汇总返回类
 */
@Data
@ApiModel
public class SamplingSummaryDataDTO {

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 层级
     */
    @ApiModelProperty("层级")
    private Integer level;

    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date startDate;

    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date endDate;

    /**
     * 详情列表
     */
    @ApiModelProperty("详情列表")
    private DataSummaryDTO dataSummaryDTO;

    /**
     * 下级分析类型
     */
    private List<SamplingSummaryDataDTO> samplingSummaryDataChildList;
}
