package com.yingfei.entity.dto.chart;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.annotation.Excel;
import com.yingfei.entity.dto.SGRP_DSC_DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 实时能力趋势图 EXCEL（统计值和原始值） 统计数据
 */
@Data
@ApiModel
@Accessors(chain = true)
public class StreamTrendInfCatchDTO {

    @ApiModelProperty("子组ID")
    private Long F_SGRP;

    @ApiModelProperty("子组时间")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date F_SGTM;

    @ApiModelProperty("产品")
    private String partName;

    @ApiModelProperty("版本")
    private String revName;

    @ApiModelProperty("过程")
    private String prcsName;

    @ApiModelProperty("测试")
    private String testName;

    @ApiModelProperty("批次")
    private String lotName;

    @ApiModelProperty("班次")
    private String shiftName;

    @ApiModelProperty("工单")
    private String jobName;

//    @ApiModelProperty("自定义描述符")
//    private String descName;

    @ApiModelProperty("描述符列表")
    private List<SGRP_DSC_DTO> sgrpDscList;


    @ApiModelProperty("开始采集时间")
    
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date F_START;

    @ApiModelProperty("结束采集时间")
    
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date F_END;

    @ApiModelProperty("cp")
    private Double F_CP;

    @Excel(name = "测试序号")
    private Integer testNo;

    @ApiModelProperty("子测试序号")
    private Integer subTestNo;

    @ApiModelProperty("实测值")
    private Double testVal;

    @ApiModelProperty("目标值")
    private Double F_TAR;

    /**
     * CP目标值(SPEC_LIM--F_CP)
     */
    @ApiModelProperty("CPK目标值1")
    private Double F_CPTAR;

    /**
     * CPK目标值(SPEC_LIM--F_CPK)
     */
    @ApiModelProperty("CPK目标值2")
    private Double F_CPKTAR;

    @Excel(name = "CPK实测值",needMerge=true)
    @ApiModelProperty("cpk")
    private String F_CPK;

    @ApiModelProperty("极差的和")
    private Double F_RANGE_SUM;

    @ApiModelProperty("短期标准差的和")
    private Double F_SD_SUM;

    @ApiModelProperty("子组数量")
    private Integer F_SGRP_COUNT;

    @ApiModelProperty("测试值数量和")
    private Integer F_VALUE_COUNT;

    @ApiModelProperty("报警事件data数量")
    private Integer F_EVNT_COUNT;

    @ApiModelProperty("报警事件data根据type拆开数量")
    private Integer F_EVNT_DETAIL_COUNT;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        StreamTrendInfCatchDTO that = (StreamTrendInfCatchDTO) o;
        return Objects.equals(F_SGRP, that.F_SGRP) && Objects.equals(F_SGTM, that.F_SGTM) && Objects.equals(partName, that.partName) && Objects.equals(revName, that.revName) && Objects.equals(prcsName, that.prcsName) && Objects.equals(testName, that.testName) && Objects.equals(lotName, that.lotName) && Objects.equals(shiftName, that.shiftName) && Objects.equals(jobName, that.jobName) && Objects.equals(sgrpDscList, that.sgrpDscList) && Objects.equals(F_START, that.F_START) && Objects.equals(F_END, that.F_END) && Objects.equals(F_CP, that.F_CP) && Objects.equals(testNo, that.testNo) && Objects.equals(subTestNo, that.subTestNo) && Objects.equals(testVal, that.testVal) && Objects.equals(F_TAR, that.F_TAR) && Objects.equals(F_CPTAR, that.F_CPTAR) && Objects.equals(F_CPKTAR, that.F_CPKTAR) && Objects.equals(F_CPK, that.F_CPK) && Objects.equals(F_RANGE_SUM, that.F_RANGE_SUM) && Objects.equals(F_SD_SUM, that.F_SD_SUM) && Objects.equals(F_SGRP_COUNT, that.F_SGRP_COUNT) && Objects.equals(F_VALUE_COUNT, that.F_VALUE_COUNT) && Objects.equals(F_EVNT_COUNT, that.F_EVNT_COUNT) && Objects.equals(F_EVNT_DETAIL_COUNT, that.F_EVNT_DETAIL_COUNT);
    }

    @Override
    public int hashCode() {
        return Objects.hash(F_SGRP, F_SGTM, partName, revName, prcsName, testName, lotName, shiftName, jobName, sgrpDscList, F_START, F_END, F_CP, testNo, subTestNo, testVal, F_TAR, F_CPTAR, F_CPKTAR, F_CPK, F_RANGE_SUM, F_SD_SUM, F_SGRP_COUNT, F_VALUE_COUNT, F_EVNT_COUNT, F_EVNT_DETAIL_COUNT);
    }
}
