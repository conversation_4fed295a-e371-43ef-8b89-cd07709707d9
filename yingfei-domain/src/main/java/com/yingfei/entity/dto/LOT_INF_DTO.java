package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.Date;

/**
* 储存批次信息表
* @TableName LOT_INF
*/
@Data
public class LOT_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_LOT;
    /**
    * 分公司主键
    */
    @ApiModelProperty("分公司主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DIV = 0L;
    /**
    * 批次关联的产品ID(PART_INF)
    */
    @ApiModelProperty("批次关联的产品ID(PART_INF)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
    * 批次关联的过程ID，IQC使用(PRCS_INF)
    */
    @ApiModelProperty("批次关联的过程ID，IQC使用(PRCS_INF)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
    * 批次名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("批次名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;
    /**
    * 批量，IQC使用，默认值为0
    */
    @ApiModelProperty("批量，IQC使用，默认值为0")
    private Integer F_COUNT;
    /**
    * 批次开放使用时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("批次开放使用时间")
    private Date F_RELEASE_TIME;

    /**
    * 批次关闭使用时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("批次关闭使用时间")
    private Date F_CLOSE_TIME;
    /**
    * 批次是否关闭标识，默认为0，代表批次开放使用，关闭时值为1。当批次被通过数据采集被创建时，创建时间为开放使用时间
    */
    @ApiModelProperty("批次是否关闭标识，默认为0，代表批次开放使用，关闭时值为1。当批次被通过数据采集被创建时，创建时间为开放使用时间")
    private Boolean F_CLOSE;
    /**
    * 批次因子，默认为1
    */
    @ApiModelProperty("批次因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("产品名称")
    private String partName;

    @ApiModelProperty("过程名称")
    private String prcsName;

    /**
     * 工厂名称
     */
    private String F_PLNT_NAME;
}
