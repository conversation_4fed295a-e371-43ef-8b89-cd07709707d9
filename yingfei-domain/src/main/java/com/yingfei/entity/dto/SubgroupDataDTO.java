package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.EVNT_INF;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 子组主信息表
 *
 * @TableName SGRP_INF
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubgroupDataDTO extends BaseEntity {

    /**
     * 记录主键
     */
    @NotNull(message = "[记录主键]不能为空")
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;
    /**
     * 工艺流程ID
     */
    @NotNull(message = "[工艺流程ID]不能为空")
    @ApiModelProperty("工艺流程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFPS;
    /**
     * 工艺节点ID
     */
    @NotNull(message = "[工艺节点ID]不能为空")
    @ApiModelProperty("工艺节点ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFND;
    /**
     * 产品ID
     */
    @NotNull(message = "[产品ID]不能为空")
    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
     * 过程ID
     */
    @NotNull(message = "[过程ID]不能为空")
    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
     * 产品版本ID
     */
    @NotNull(message = "[产品版本ID]不能为空")
    @ApiModelProperty("产品版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_REV;
    /**
     * 产品批次
     */
    @NotNull(message = "[产品批次]不能为空")
    @ApiModelProperty("产品批次")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_LOT;
    /**
     * 工作ID
     */
    @NotNull(message = "[工作ID]不能为空")
    @ApiModelProperty("工作ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JOB;
    /**
     * 班次ID
     */
    @NotNull(message = "[班次ID]不能为空")
    @ApiModelProperty("班次ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SHIFT;
    /**
     * 子组样本量
     */
    @NotNull(message = "[子组样本量]不能为空")
    @ApiModelProperty("子组样本量")
    private Integer F_SGSZ;
    /**
     * 子组时间
     */
    @NotNull(message = "[子组时间]不能为空")

    @ApiModelProperty("子组时间")
    private Date F_SGTM;
    /**
     * 失效标识 0=激活，1=失效
     */
    @NotNull(message = "[失效标识 0=激活，1=失效]不能为空")
    @ApiModelProperty("失效标识 0=激活，1=失效")
    private Integer F_FLAG;
    /**
     * 是否删除标记，默认值为0
     */
    @NotNull(message = "[是否删除标记，默认值为0]不能为空")
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();

    @ApiModelProperty("检验类型id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_INSPECTION_TYPE;
    /**
     * 创建用户
     */
    @NotNull(message = "[创建用户]不能为空")
    @ApiModelProperty("创建用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 编辑用户
     */
    @NotNull(message = "[编辑用户]不能为空")
    @ApiModelProperty("编辑用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;


    /**
     * 子计划数量
     */
    private Integer F_NUM;

    /**
     * 检查计划id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_INSP_PLAN;

    /**
     * 检查计划名称
     */
    private String planName;
    /**
     * 产品名称
     */
    private String partName;
    /**
     * 过程名称
     */
    private String prcsName;

    @ApiModelProperty("子组对应的批次名称")
    private String lotName;

    @ApiModelProperty("子组对应的班次名称")
    private String shiftName;

    @ApiModelProperty("子组对应的工作名称")
    private String jobName;

    @ApiModelProperty("子组对应的版本名称")
    private String ptrvName;

    @ApiModelProperty("子组对应的测试名称")
    private String testName;

    @ApiModelProperty("产品序列号名称")
    private String snName;

    @ApiModelProperty("检验类型名称")
    private String instName;
    /**
     * 测试id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;

    /**
     * 测试类型
     */
    private Integer testType;

    /**
     * 测试json对象
     */
    private String testData;

    /**
     * 测试对象列表
     */
    @ApiModelProperty("测试对象列表")
    private List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList;

    /**
     * 单个测试里的测试值对象
     */
    @ApiModelProperty("单个测试对象")
    private SGRP_VAL_DTO sgrpValDto;

    /**
     * 单个测试值对象
     */
    @ApiModelProperty("单个测试值对象")
    private SGRP_VAL_CHILD_DTO.Test testValDto;

    @ApiModelProperty("子组对应测试")
    private List<SGRP_VAL_DTO> sgrpValDtoList;

    /**
     * 描述符列表
     */
    @ApiModelProperty("描述符列表")
    private List<SGRP_DSC_DTO> sgrpDscList;

    /**
     * 子组备注列表
     */
    @ApiModelProperty("子组备注列表")
    private List<SGRP_CMT_DTO> sgrpCmtList;

    /**
     * 是否触发报警
     */
    @ApiModelProperty("是否触发报警")
    private Boolean isAlarm = false;

    /**
     * 班次
     */
    private SHIFT_DAT_DTO shiftDatDto;

    /**
     * 批次
     */
    private LOT_INF_DTO lotInfDto;

    /**
     * 工单
     */
    private JOB_DAT_DTO jobDatDto;

    /**
     * 子组报警信息
     */
    private List<EVNT_INF> evntInfList;

    @ApiModelProperty("动态表头")
    private Map<String, String> headerMap;

    @ApiModelProperty("测试值状态(0:正常,1:大于上公差限,2:小于下公差限)))")
    private Integer status;

    /**
     * 抽样唯一标识
     */
    private String F_SAMPLE_ID;

    /**
     * 子计划是否完成状态(0:未完成 1:已完成)
     */
    private Integer F_FINISH_STATUS;

    private DataSummaryDTO dataSummaryDTO;

    /**
     * 工厂id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;


    @ApiModelProperty("子组备注")
    private List<SGRP_CMT_DTO> specCmtDtoList;

    public SubgroupDataDTO(Long f_PART, Long f_REV, Long f_TEST, Long f_PRCS) {
        F_PART = (ObjectUtils.isEmpty(f_PART)?0L:f_PART);
        F_REV  = (ObjectUtils.isEmpty(f_REV)?0L:f_REV);
        F_TEST = (ObjectUtils.isEmpty(f_TEST)?0L:f_TEST);
        F_PRCS = (ObjectUtils.isEmpty(f_PRCS)?0L:f_PRCS);
    }
}
