package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.entity.domain.BPM_PROCESS_INSTANCE;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.EVNT_INF;
import com.yingfei.entity.domain.TAG_LINK;
import com.yingfei.entity.vo.SubgroupDataVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
* 报警信息表
* @TableName EVNT_INF
*/
@Data
public class EVNT_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EVNT;

    @ApiModelProperty("报警对应的事件类型和事件名称json  事件类型(1:公差限 2:控制限)")
    private String F_DATA;
    /**
    * 产品ID
    */
    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
    * 过程ID
    */
    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
    * 测试ID
    */
    @ApiModelProperty("测试ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;
    /**
    * 报警发生时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("报警发生时间")
    private Date F_EVTM;
    /**
    * 报警对应的子组时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("报警对应的子组时间")
    private Date F_SGTM;
    /**
    * 事件对应的子组ID。如果事件类型与子组无关，或子组已不存存在（例如，删除子组），则值为0
    */
    @ApiModelProperty("事件对应的子组ID。如果事件类型与子组无关，或子组已不存存在（例如，删除子组），则值为0")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;
    /**
    * 异常原因ID
    */
    @ApiModelProperty("异常原因ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RTCS;
    /**
    * 异常原因时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("异常原因时间")
    private Date F_RCTM;
    /**
    * 改善措施ID
    */
    @ApiModelProperty("改善措施ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RSAT;
    /**
    * 改善措施时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("改善措施时间")
    private Date F_RSTM;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 解决状态(0:未解决  1:已解决)
     */
    private Integer F_STATUS;


    @ApiModelProperty("产品名称")
    private String partName;

    @ApiModelProperty("过程名称")
    private String prcsName;

    @ApiModelProperty("测试名称")
    private String testName;

    @ApiModelProperty("异常原因名称")
    private String rtcsName;

    @ApiModelProperty("改善措施名称")
    private String rsatName;

    private Set<TAG_LINK> tagLinkSet;

    @ApiModelProperty("过程事件对应的报警流程")
    private List<BPM_PROCESS_INSTANCE> bpmProcessInstanceList = new ArrayList<>();

    public static EVNT_INF init(SubgroupDataVO subgroupDataVO,String F_DATA){
        EVNT_INF evntInf = new EVNT_INF();
        evntInf.setF_PART(subgroupDataVO.getF_PART());
        evntInf.setF_PRCS(subgroupDataVO.getF_PRCS());
        evntInf.setF_TEST(subgroupDataVO.getSgrpValChildDto().getTestId());
        evntInf.setF_EVTM(DateUtils.getNowDate());
        evntInf.setF_SGTM(subgroupDataVO.getF_SGTM());
        evntInf.setF_SGRP(subgroupDataVO.getF_SGRP());
        evntInf.setF_DATA(F_DATA);
        evntInf.setF_CRUE(subgroupDataVO.getF_CRUE());
        evntInf.setF_EDUE(subgroupDataVO.getF_CRUE());
        return evntInf;
    }
}
