package com.yingfei.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.annotation.Excel;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.PART_REV;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
* 产品信息表
* @TableName PART_INF
*/
@Data
public class PART_INF_DTO extends BaseEntity {

    /**
    * 记录主键
    */
    @NotNull(message="[记录主键]不能为空")
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
    * 工厂ID（产品归属于工厂这一层级）
    */
    @NotNull(message="[工厂ID（产品归属于工厂这一层级）]不能为空")
    @ApiModelProperty("工厂ID（产品归属于工厂这一层级）")
    @Excel(name = "工厂名称")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;
    /**
    * 产品名称
    */
    @NotBlank(message="[产品名称]不能为空")
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("产品名称")
    @Length(max= 100,message="编码长度不能超过100")
    @Excel(name = "产品名称")
    private String F_NAME;
    /**
    * 产品详细名称
    */
    @NotBlank(message="[产品详细名称]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("产品详细名称")
    @Length(max= 255,message="编码长度不能超过255")
    @Excel(name = "产品详细名称")
    private String F_LONG_NAME;
    /**
    * 产品图片地址
    */
    @NotBlank(message="[产品图片地址]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("产品图片地址")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_IMAGE;
    /**
    * 因子，默认为1
    */
    @NotNull(message="[因子，默认为1]不能为空")
    @ApiModelProperty("因子，默认为1")
    @Excel(name = "因子(不填默认1)")
    private Double F_FACTOR = 1D;
    /**
    * 是否删除标记，默认值为0
    */
    @NotNull(message="[是否删除标记，默认值为0]不能为空")
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @NotNull(message="[记录创建用户ID]不能为空")
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @NotNull(message="[记录编辑用户ID]不能为空")
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;


    @ApiModelProperty("产品版本信息")
    private List<PART_REV> partRevList;

    @ApiModelProperty("标签数量")
    private Integer tagCount;

    @ApiModelProperty("版本名称")
    @Excel(name = "版本名称")
    private String partRevName;

    @ApiModelProperty("工厂名称")
    private String F_PLNT_NAME;

    @ApiModelProperty("版本id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long partRevId;

    @ApiModelProperty("版本开始时间")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @Excel(name = "版本开始时间(选填)")
    private Date partRevStartTime;

    @ApiModelProperty("版本结束时间(选填)")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @Excel(name = "版本结束时间(选填)")
    private Date partRevEndTime;

    @ApiModelProperty("版本创建时间")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date partRevCreateTime;

    @ApiModelProperty("版本修改时间")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date partRevEditTime;

    @ApiModelProperty("所选标签列表")
    private List<TAG_DAT_DTO> tagDatDtoList;

    private PART_REV partRev;
}
