package com.yingfei.entity.enums;

import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.entity.dto.CalculatedControlLimit;
import com.yingfei.entity.vo.DataPointVO;
import lombok.Getter;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 控制限报警枚举
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
public enum StatisticalViolationTypeEnum {

    IGNORE_SUBGROUP(1, "已忽略子组", "Excl", "", "", "rule6", 0, 0, 0),
    ABOVE_CEILING(2, "一个点超出控制上限", ">UCL", "", "", "outOfCtrlLimit", 0, 1, 0),
    BELOW_CEILING(3, "一个点超出控制下限", "<LCL", "", "", "outOfCtrlLimit", 0, 2, 0),
    ABOVE_ZONE_A(4, "分布在中心线上侧的B区以外", "2/3 UZA", "", "", "withinSigmaZone", 1, 1, 0),
    BELOW_ZONE_A(5, "分布在中心线下侧的B区以外", "2/3 LZA", "", "", "withinSigmaZone", 1, 2, 0),
    ABOVE_ZONE_B(6, "分布在中心线上侧的C区以外", "4/5 UZB", "", "", "withinSigmaZone", 2, 1, 0),
    BELOW_ZONE_B(7, "分布在中心线下侧的C区以外", "4/5 LZB", "", "", "withinSigmaZone", 2, 2, 0),
    ABOVE_CENTER_LINE(8, "连续分布在中心线上侧", "8>CL", "", "", "withinSigmaZone", 3, 1, 0),
    BELOW_CENTER_LINE(9, "连续分布在中心线下侧", "8<CL", "", "", "withinSigmaZone", 3, 2, 0),
    WITHIN_ZONE_C(10, "连续分布在中心线两侧C区", "15 In C", "", "", "inOrOutZoneC", 4, 1, 0),
    OUTSIDE_ZONE_C(11, "连续分布在中心线两侧但不在C区", "8 out C", "", "", "inOrOutZoneC", 4, 2, 0),
    CONTINUOUS_RISE(12, "数据点连续上升", "6升", "", "", "continuousUpDown", 0, 1, 1),
    CONTINUOUS_DECLINE(13, "数据点连续下降", "6降", "", "", "continuousUpDown", 0, 2, 1),
    OSCILLATE_UP_AND_DOWN(14, "相邻点上下交替", "14 Osc", "", "", "oscillatingUpDown", 0, 0, 1),
    NO_VARIATION_IN_VALUES(15, "数值无变差", "NoVar", "", "", "", 0, 0, 0),
    WITHIN_ZONE_A(16, "在相对A区域内的点(黄色区域)", "", "", "", "", 0, 0, 0),
    MEAN_ABOVE_THE_TARGET_VALUE(17, "连续均值在目标值以上", "MA Below Target", "", "", "", 0, 1, 0),
    MEAN_BELOW_THE_TARGET_VALUE(18, "连续均值在目标值以下", "MA Below Target", "", "", "", 0, 2, 0),
    //    MEAN_ABOVE_LSC(19, "连续均值高于LSC", "", "", "", 0, 1, 0),
//    MEAN_BELOW_LSC(20, "连续均值低于LSC", "", "", "", 0, 2, 0),
    CPK_IS_BELOW_THE_TARGET(21, "CP/CPK在目标下方", "Cp/Cpk Below Target", "", "", "processCapabilityBelowTarget", 0, 0, 0),


    ;

    private final Integer code;
    private final String description;

    /**
     * 缩写
     */
    private final String abbr;

    /**
     * 实现类
     */
    private String className;

    /**
     * 监控实现类
     */
    private String monitorClassName;

    /**
     * 实现方法
     */
    private String methodName;

    /**
     * 根据实现方法变化
     * 1.区间(0:不处理 1:A区 2:B区 3:中心线 4:C区)
     */
    private Integer type;

    /**
     * 区域(0:无 1:上(内) 2:下(外))
     */
    private Integer area;

    /**
     * 数据点特殊处理(count 加多少个点)
     */
    private Integer dataPointDispose;

    StatisticalViolationTypeEnum(int code, String description, String abbr, String className, String monitorClassName,
                                 String methodName, Integer type, Integer area, Integer dataPointDispose) {
        this.code = code;
        this.description = description;
        this.abbr = abbr;
        this.className = "com.yingfei.entity.util.WarningRuleUtil";
        this.monitorClassName = "com.yingfei.entity.util.MonitorWarningRuleUtil";
        this.methodName = methodName;
        this.type = type;
        this.area = area;
        this.dataPointDispose = dataPointDispose;
    }

    public static Map<Integer, String> getMap() {
        HashMap<Integer, String> map = new HashMap<>();
        for (StatisticalViolationTypeEnum value : StatisticalViolationTypeEnum.values()) {
            map.put(value.getCode(), value.getDescription());
        }
        return map;
    }

    public static StatisticalViolationTypeEnum getType(Integer code) {
        for (StatisticalViolationTypeEnum e : StatisticalViolationTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return StatisticalViolationTypeEnum.IGNORE_SUBGROUP;
    }

    /**
     * 执行动态方法，参数为map 方便实现对应业务
     *
     * @param ruleTypeEnum 报警规则枚举
     * @param params       map 参数 将需要的参数放入map中
     */
    public static void invoke(StatisticalViolationTypeEnum ruleTypeEnum, List<DataPointVO> dataPointVOList, Map<String, Object> params) {
        try {
            Class<?> aClass = Class.forName(ruleTypeEnum.getClassName());
            for (Method method : aClass.getMethods()) {
                if (StringUtils.isEmpty(ruleTypeEnum.getMethodName())) continue;
                if (ruleTypeEnum.getMethodName().equals(method.getName())) {
                    method.invoke(aClass, dataPointVOList, params);
                    break;
                }
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            throw new BaseException("REFLECTION_IMPLEMENTATION_CLASS_EXCEPTION");
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            throw new BaseException("ACCESS_TO_THE_METHOD_IS_DENIED");
        } catch (InvocationTargetException e) {
            e.printStackTrace();
            throw new BaseException("EXCEPTION_OCCURRED_WHILE_EXECUTING_THE_ALARM_METHOD");
        }
    }

    /**
     * 数据监控报警执行方法
     *
     * @param ruleTypeEnum
     * @param calculatedControlLimits
     * @param params
     */
    public static void monitorInvoke(StatisticalViolationTypeEnum ruleTypeEnum, List<CalculatedControlLimit> calculatedControlLimits, Map<String, Object> params) {
        try {
            Class<?> aClass = Class.forName(ruleTypeEnum.getMonitorClassName());
            for (Method method : aClass.getMethods()) {
                if (StringUtils.isEmpty(ruleTypeEnum.getMethodName())) continue;
                if (ruleTypeEnum.getMethodName().equals(method.getName())) {
                    method.invoke(aClass, calculatedControlLimits, params);
                    break;
                }
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
            throw new BaseException("REFLECTION_IMPLEMENTATION_CLASS_EXCEPTION");
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            throw new BaseException("ACCESS_TO_THE_METHOD_IS_DENIED");
        } catch (InvocationTargetException e) {
            e.printStackTrace();
            throw new BaseException("EXCEPTION_OCCURRED_WHILE_EXECUTING_THE_ALARM_METHOD");
        }
    }

}
