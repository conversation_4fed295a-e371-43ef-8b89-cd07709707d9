package com.yingfei.entity.util.controlChart;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SGRP_VAL_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.vo.DataPointVO;
import com.yingfei.entity.vo.SubgroupDataVO;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制图分组类型-------->P图
 */
@Service
public class ControlChartGroupP implements ControlChartCalculateService {


    /**
     * 图类P图-------> 数据点计算公式为: 子组内测试值/样本量
     *
     * @param subgroupDataDTOList   子组测试数据,要按子组时间正序排列
     * @param chartTypeEnum 图类型
     */
    @Override
    public List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum) {
        List<DataPointVO> list = new ArrayList<>();
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            /*将F_VAL的值求和*/
//            Double aDouble = subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getTestList().stream()
//                    .map(SGRP_VAL_CHILD_DTO.Test::getTestVal).reduce(Double::sum).orElse(0d);
//            /*获取样本量*/
//            Double fSgsz = Double.valueOf(subgroupDataDTO.getF_SGSZ());
            list.add(DataPointVO.getDataPoint(subgroupDataDTO, subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getAverage()));
        });
        return list;
    }

    /**
     * F_SP*d2
     *
     * @return
     */
    @Override
    public Double controlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_MEAN();
    }

    /**
     * CL*D4
     *
     * @return
     */
    @Override
    public Double controlLimitUCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_MEAN() +
                3 * Math.sqrt((controlLimitDto.getF_MEAN() * (1 - controlLimitDto.getF_MEAN())) / controlLimitDto.getN());
    }

    /**
     * 0
     */
    @Override
    public Double controlLimitLCL(ControlLimitDTO controlLimitDto) {
        double v = controlLimitDto.getF_MEAN() -
                3 * Math.sqrt((controlLimitDto.getF_MEAN() * (1 - controlLimitDto.getF_MEAN())) / controlLimitDto.getN());
        return v < 0 ? 0 : v;
    }

    @Override
    public Double historyControlLimitCL(ControlLimitDTO controlLimitDto) {
        return null;
    }

    @Override
    public Double historyControlLimitUCL(ControlLimitDTO controlLimitDto) {
        return null;
    }

    @Override
    public Double historyControlLimitLCL(ControlLimitDTO controlLimitDto) {
        return null;
    }
}
