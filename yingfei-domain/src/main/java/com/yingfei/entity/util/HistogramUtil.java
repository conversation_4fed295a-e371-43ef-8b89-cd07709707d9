package com.yingfei.entity.util;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SGRP_VAL_DTO;
import com.yingfei.entity.enums.TEST_INF_TYPEEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @createTime 2023-12-07 下午 3:43
 * @description 直方图统计值工具类
 */
public class HistogramUtil {


    /**
     * 计算直方图 子组大小(样本量)  测试数量/子组数量
     * 计算控制图 子组大小(样本量)  测试数量/子组数量  控制图的测试数量是 F_SBNO=0的数量
     *
     * @param testNum     测试数量
     * @param subgroupNum 子组数量
     */
    public static Double getSubgroupSize(Integer testNum, Integer subgroupNum) {
        return Double.valueOf(testNum) / Double.valueOf(subgroupNum);
    }

    /**
     * 计算直方图 短期标准差
     * 1.S/c4  S 所有子组标准差的平均值(用长期标准差算)  标准差法
     * 2.R/d2  R 每个子组最大测试值减最小值 的差值 的相加的平均值  极差法(默认用这个)
     * 3.mr/d2 mr 是每个子组后一个点减前一个点绝对值 相加的平均值  极差法
     * 1. 样本量>9时，使用公式1
     * 2. 样本量<=9时，使用公式2
     * 3. 样本量=1时，使用公式3
     * 注：c4和d2为纠偏系数，跟样本量有关。如果样本量为整数，可通过查表获得；如果样本量为小数，则通过插值法计算出来
     *
     * @param subgroupSize    子组大小(样本量)
     * @param sgrpValDtoList  子组对应的测试值集合
     * @param shortSdTermType 1:自适应 2:极差法 3:标准差法 4:合并标准差法(标准差法平方+极差法平方的和再开方)
     */
    public static Double getShortTermStandardDeviation(Double subgroupSize, List<SGRP_VAL_DTO> sgrpValDtoList, Integer shortSdTermType) {
        /*分子*/
        Double molecule = 0d;
        List<Double> list = new ArrayList<>();
        if (shortSdTermType == 2) {
            subgroupSize = 9d;
        } else if (shortSdTermType == 3) {
            subgroupSize = 10d;
        } else if (shortSdTermType == 4) {
            Double sd = getShortTermStandardDeviation(10d, sgrpValDtoList, 3);
            Double mr = getShortTermStandardDeviation(9d, sgrpValDtoList, 2);
            return Math.sqrt(Math.pow(sd, 2d) + Math.pow(mr, 2d));
        }
        if (subgroupSize > 9) {
            /*计算S的子集合*/
            sgrpValDtoList.forEach(sgrpValDto -> {
                if (sgrpValDto.getSgrpValChildDto().getSd() != null) {
                    list.add(sgrpValDto.getSgrpValChildDto().getSd());
                }
            });
        } else if (subgroupSize >= 1 && subgroupSize < 2) {
            /*计算MR的子集合*/
            AtomicReference<Double> d = new AtomicReference<>(0d);
            AtomicInteger i = new AtomicInteger(0);
            sgrpValDtoList.forEach(sgrpValDto -> {
                final List<SGRP_VAL_CHILD_DTO.Test> testList = sgrpValDto.getSgrpValChildDto().getTestList();
                if(CollectionUtils.isNotEmpty(testList)){
                    Double aDouble = testList
                            .get(0).getTestVal();
                    if (d.get() == 0d && i.get() == 0) {
                        d.set(aDouble);
                    } else {
                        list.add(Math.abs(BigDecimal.valueOf(aDouble).subtract(BigDecimal.valueOf(d.get())).doubleValue()));
                        d.set(aDouble);
                    }
                    i.getAndIncrement();
                }
            });
            if (CollectionUtils.isEmpty(list)) {
                return 0d;
            }
        } else {
            /*计算R的子集合*/
            sgrpValDtoList.forEach(sgrpValDto -> {
                if (sgrpValDto.getSgrpValChildDto().getRange() != null) {
                    list.add(sgrpValDto.getSgrpValChildDto().getRange());
                }
            });
        }


        molecule = getSOrR(list);

        /*分母*/
        Double correctionData = CorrectionConstantUtil.getCorrectionData(subgroupSize);
       // System.out.println("短期标准差分母correctionData ------------> " + correctionData + "------样本量----->" + subgroupSize);
        if (Double.isNaN(correctionData) || Double.isInfinite(correctionData)) {
            return 0d;
        }
        return molecule / correctionData;
    }


    /**
     * 计算直方图 短期标准差(件内西格玛计算)
     * 1.S/c4  S 所有子组标准差的平均值(用长期标准差算)  标准差法
     * 2.R/d2  R 每个子组最大测试值减最小值 的差值 的相加的平均值  极差法(默认用这个)
     * 3.mr/d2 mr 是每个子组后一个点减前一个点绝对值 相加的平均值  极差法
     * 1. 样本量>9时，使用公式1
     * 2. 样本量<=9时，使用公式2
     * 3. 样本量=1时，使用公式3
     * 注：c4和d2为纠偏系数，跟样本量有关。如果样本量为整数，可通过查表获得；如果样本量为小数，则通过插值法计算出来
     *
     * @param subTestSize     子测试平均样本量
     * @param sgrpValDtoList  子组对应的测试值集合
     * @param shortSdTermType 1:自适应 2:极差法 3:标准差法 4:合并标准差法(标准差法平方+极差法平方的和再开方)
     * @see com.yingfei.entity.enums.ShortSdTermTypeEnum
     */
    public static Double getWithingShortTermStandardDeviation(Double subTestSize, List<SGRP_VAL_DTO> sgrpValDtoList, Integer shortSdTermType) {
        /*分子*/
        Double molecule = 0d;
        List<Double> list = new ArrayList<>();
        if (shortSdTermType == 2) {
            subTestSize = 9d;
        } else if (shortSdTermType == 3) {
            subTestSize = 10d;
        } else if (shortSdTermType == 4) {
            Double sd = getShortTermStandardDeviation(10d, sgrpValDtoList, 3);
            Double mr = getShortTermStandardDeviation(9d, sgrpValDtoList, 2);
            return Math.sqrt(Math.pow(sd, 2d) + Math.pow(mr, 2d));
        }
        if (subTestSize > 9) {
            /*计算S的子集合*/
            sgrpValDtoList.forEach(sgrpValDto -> {
                if (sgrpValDto.getSgrpValChildDto().getWithinPieceSdMax() != null) {
                    list.add(sgrpValDto.getSgrpValChildDto().getWithinPieceSdMax());
                }
            });
        } else if (subTestSize >= 1 && subTestSize < 2) {
            /*计算MR的子集合*/
            AtomicReference<Double> d = new AtomicReference<>(0d);
            AtomicInteger i = new AtomicInteger(0);
            sgrpValDtoList.forEach(sgrpValDto -> {
                Double aDouble = sgrpValDto.getSgrpValChildDto().getTestList().get(0).getTestVal();
                if (d.get() == 0d && i.get() == 0) {
                    d.set(aDouble);
                } else {
                    list.add(Math.abs(BigDecimal.valueOf(aDouble).subtract(BigDecimal.valueOf(d.get())).doubleValue()));
                    d.set(aDouble);
                }
                i.getAndIncrement();
            });
            if (CollectionUtils.isEmpty(list)) {
                return 0d;
            }
        } else {
            /*计算R的子集合*/
            sgrpValDtoList.forEach(sgrpValDto -> {
                if (sgrpValDto.getSgrpValChildDto().getWithinPieceRangeMax() != null) {
                    list.add(sgrpValDto.getSgrpValChildDto().getWithinPieceRangeMax());
                }
            });
        }


        molecule = getSOrR(list);

        /*分母*/
        Double correctionData = CorrectionConstantUtil.getCorrectionData(subTestSize);
       // System.out.println("短期标准差分母correctionData ------------> " + correctionData + "------子测试平均样本量----->" + subTestSize);
        if (Double.isNaN(correctionData) || Double.isInfinite(correctionData)) {
            return 0d;
        }
        return molecule / correctionData;
    }

    /**
     * 计算直方图 计算短期标准差中S或者R的值
     * S 所有子组标准差的平均值(用长期标准差算)
     * R 每个子组最大测试值减最小值 的差值 的相加的平均值
     * mr 是每个子组后一个值减前一个值的绝对值 相加的平均值
     *
     * @param list 计算后的子组测试值列表
     */
    public static Double getSOrR(List<Double> list) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        list.forEach(stats::addValue);
        /*对list求和*/

        return stats.getMean();
    }

    /**
     * 计算直方图 计算长期标准差
     *
     * @param list 测试值列表
     */
    public static Double getLongTermStandardDeviation(List<Double> list) {
        DescriptiveStatistics stats = new DescriptiveStatistics();
        list.forEach(stats::addValue);
        return stats.getStandardDeviation();
    }

    /**
     * 计算直方图 稳健系数(Robustness)
     * (短期标准差/长期标准差)*100
     * 如果长期标准差为0，则稳健系数的值为Null
     *
     * @param shortTermStandardDeviation 短期标准差
     * @param longTermStandardDeviation  长期标准差
     */
    public static Double getRobustness(Double shortTermStandardDeviation, Double longTermStandardDeviation) {
        if (longTermStandardDeviation == 0d) {
            return null;
        }
        return (shortTermStandardDeviation / longTermStandardDeviation) * 100;
    }

    /**
     * 变异系数(CoVar)
     * (长期标准差/均值)*100
     * 如果均值为0，则CoVar的值为Null
     *
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getCoVar(Double mean, Double longTermStandardDeviation) {
        if (mean == 0d) {
            return null;
        }
        return (longTermStandardDeviation / mean) * 100d;
    }

    /**
     * 计算直方图 Ca
     * 0.5*(过程均值-目标值)/(USL-LSL)
     *
     * @param mean 均值
     * @param tar  目标值
     * @param usl  公差上限
     * @param lsl  公差下限
     */
    public static Double getCa(Double mean, Double tar, Double usl, Double lsl) {
        if (tar == null || usl == null || lsl == null) {
            return null;
        }
        return 0.5 * (mean - tar) / (usl - lsl);
    }

    /**
     * 计算直方图 Z USL
     * (公差上限-均值)/长期标准差
     * 如果短期标准差的值为0，则Z USL的值为Null
     *
     * @param usl                       公差上限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getZUsl(Double usl, Double mean, Double longTermStandardDeviation) {
        if (longTermStandardDeviation == 0d || usl == null) {
            return null;
        }
        return (usl - mean) / longTermStandardDeviation;
    }

    /**
     * 计算直方图 Z 目标值
     * (目标值-均值)/长期标准差
     * 如果短期标准差的值为0，则Z 目标值的值为Null
     *
     * @param tar                       目标值
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getZTargetValue(Double tar, Double mean, Double longTermStandardDeviation) {
        if (longTermStandardDeviation == 0d || tar == null) {
            return null;
        }
        return (tar - mean) / longTermStandardDeviation;
    }

    /**
     * 计算直方图 Z LSL
     * (均值-公差下限)/长期标准差
     * 如果短期标准差的值为0，则Z LSL的值为Null
     *
     * @param lsl                       公差下限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getZLsl(Double lsl, Double mean, Double longTermStandardDeviation) {
        if (longTermStandardDeviation == 0d || lsl == null) {
            return null;
        }
        return (mean - lsl) / longTermStandardDeviation;
    }

    /**
     * 实际值>USL
     * 数据集中数值超公差上限的百分比
     * <p>
     * (所有测试值中大于上公差限的数量/测试总数量)*100
     *
     * @param list 测试值列表
     * @param usl  公差上限
     */
    public static Double getUslPercent(List<Double> list, Double usl) {
        if (usl == null) {
            return null;
        }
        return (list.stream().filter(v -> v > usl).count() / (double) list.size()) * 100;
    }

    /**
     * 实际值<LSL
     * 数据集中数值超公差下限的百分比
     * <p>
     * (所有测试值中小于下公差限的数量/测试总数量)*100
     *
     * @param list 测试值列表
     * @param lsl  公差下限
     */
    public static Double getLslPercent(List<Double> list, Double lsl) {
        if (lsl == null) {
            return null;
        }
        return (list.stream().filter(v -> v < lsl).count() / (double) list.size()) * 100;
    }

    /**
     * 实际值总数
     * 实际值>USL+实际值<LSL
     *
     * @param uslPercent 实际值>USL
     * @param lslPercent 实际值<LSL
     */
    public static Double getActualValueTotal(Double uslPercent, Double lslPercent) {
        if (uslPercent == null && lslPercent == null) {
            return null;
        } else if (uslPercent == null) {
            return lslPercent;
        } else if (lslPercent == null) {
            return uslPercent;
        }
        return BigDecimal.valueOf(uslPercent).add(BigDecimal.valueOf(lslPercent)).doubleValue();
    }

    /**
     * 实际PPM
     * 实际值总数*10000
     *
     * @param actualValueTotal 实际值总数
     */
    public static Double getActualPPM(Double actualValueTotal) {
        if (actualValueTotal == null) {
            return null;
        }
        return actualValueTotal * 10000;
    }

    /**
     * 预期值>USL
     * (1-NORML.DIST(USL,均值,长期标准差))*100
     *
     * @param usl                       公差上限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getExpectUsl(Double usl, Double mean, Double longTermStandardDeviation) {
        if (usl == null || longTermStandardDeviation == 0d) {
            return null;
        }
        NormalDistribution normalDistribution = new NormalDistribution(mean, longTermStandardDeviation);
        return (1 - normalDistribution.cumulativeProbability(usl)) * 100;
    }

    /**
     * 预期值<LSL
     * (NORML.DIST(LSL,均值,长期标准差))*100
     *
     * @param lsl                       公差下限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getExpectLsl(Double lsl, Double mean, Double longTermStandardDeviation) {
        if (lsl == null || longTermStandardDeviation == 0d) {
            return null;
        }
        NormalDistribution normalDistribution = new NormalDistribution(mean, longTermStandardDeviation);
        return (normalDistribution.cumulativeProbability(lsl)) * 100;
    }

    /**
     * 预期值总数
     * 预期值>USL+预期值<LSL
     *
     * @param expectUsl 预期值>USL
     * @param expectLsl 预期值<LSL
     */
    public static Double getExpectValueTotal(Double expectUsl, Double expectLsl) {
        if (expectUsl == null && expectLsl == null) {
            return null;
        } else if (expectUsl == null) {
            return expectLsl;
        } else if (expectLsl == null) {
            return expectUsl;
        }
        return BigDecimal.valueOf(expectUsl).add(BigDecimal.valueOf(expectLsl)).doubleValue();
    }

    /**
     * 预期PPM
     * 预期值总数*10000
     *
     * @param expectValueTotal 预期值总数
     */
    public static Double getExpectPPM(Double expectValueTotal) {
        if (expectValueTotal == null) {
            return null;
        }
        return expectValueTotal * 10000;
    }

    /**
     * 计算直方图 Cp
     * 1. (USL-LSL)/(6*短期标准差)
     * 2. (USL-均值)/(3*短期标准差)
     * 3. (均值-LSL)/(3*短期标准差)
     * <p>
     * 1. 双边公差使用公式一
     * 2. 单边上限，即只有上公差的时候，使用公式二
     * 3. 单边下限，即只有下公差的时候，使用公式三
     * 注：短期标准差为0时，Cp值为Null
     *
     * @param usl                        公差上限
     * @param lsl                        公差下限
     * @param mean                       均值
     * @param shortTermStandardDeviation 短期标准差
     */
    public static Double getCp(Double usl, Double lsl, Double mean, Double shortTermStandardDeviation) {
        if (shortTermStandardDeviation == null || shortTermStandardDeviation == 0d) {
            return null;
        }
        if (usl != null && lsl != null) {
            return (usl - lsl) / (6 * shortTermStandardDeviation);
        } else if (usl != null) {
            return (usl - mean) / (3 * shortTermStandardDeviation);
        } else if (lsl != null) {
            return (mean - lsl) / (3 * shortTermStandardDeviation);
        }
        return null;
    }

    /**
     * 计算直方图 Pp
     * 1. (USL-LSL)/(6*长期标准差)
     * 2. (USL-均值)/(3*长期标准差)
     * 3. (均值-LSL)/(3*长期标准差)
     * <p>
     * 1. 双边公差使用公式一
     * 2. 单边上限，即只有上公差的时候，使用公式二
     * 3. 单边下限，即只有下公差的时候，使用公式三
     * 注：长期标准差为0时，Pp值为Null
     *
     * @param usl                       公差上限
     * @param lsl                       公差下限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getPp(Double usl, Double lsl, Double mean, Double longTermStandardDeviation) {
        if (longTermStandardDeviation == 0d) {
            return null;
        }
        if (usl != null && lsl != null) {
            return (usl - lsl) / (6 * longTermStandardDeviation);
        } else if (usl != null) {
            return (usl - mean) / (3 * longTermStandardDeviation);
        } else if (lsl != null) {
            return (mean - lsl) / (3 * longTermStandardDeviation);
        }
        return null;
    }

    /**
     * 计算直方图 Cr
     * 1/Cp Cp值为0或者Null时，Cr的值为Null
     *
     * @param cp Cp
     */
    public static Double getCr(Double cp) {
        if (cp == null || cp == 0d) {
            return null;
        }
        return 1 / cp;
    }

    /**
     * 计算直方图 Pr
     * 1/Pp Pp值为0或者Null时，Pr的值为Null
     *
     * @param pp Pp
     */
    public static Double getPr(Double pp) {
        if (pp == null || pp == 0d) {
            return null;
        }
        return 1 / pp;
    }

    /**
     * 计算直方图 Cpk
     * 1. {(USL-均值)/(3*短期标准差), (均值-LSL)/(3*短期标准差)}中最小值
     * 2. (USL-均值)/(3*短期标准差)
     * 3. (均值-LSL)/(3*短期标准差)
     * <p>
     * 1. 双边公差使用公式一
     * 2. 单边上限，即只有上公差的时候，使用公式二
     * 3. 单边下限，即只有下公差的时候，使用公式三
     *
     * @param usl                        公差上限
     * @param lsl                        公差下限
     * @param mean                       均值
     * @param shortTermStandardDeviation 短期标准差
     */
    public static Double getCpk(Double usl, Double lsl, Double mean, Double shortTermStandardDeviation) {
        if (shortTermStandardDeviation == null || shortTermStandardDeviation == 0d) {
            return null;
        }
        if (usl != null && lsl != null) {
            return Math.min((usl - mean) / (3 * shortTermStandardDeviation), (mean - lsl) / (3 * shortTermStandardDeviation));
        } else if (usl != null) {
            return (usl - mean) / (3 * shortTermStandardDeviation);
        } else if (lsl != null) {
            return (mean - lsl) / (3 * shortTermStandardDeviation);
        }
        return null;
    }

    /**
     * 计算直方图 Cpu
     * (USL-均值)/(3*短期标准差)
     * <p>
     * 如果没有上公差，则Cpu的值为Null
     *
     * @param usl                        公差上限
     * @param mean                       均值
     * @param shortTermStandardDeviation 短期标准差
     */
    public static Double getCpu(Double usl, Double mean, Double shortTermStandardDeviation) {
        if (usl == null || shortTermStandardDeviation == 0d) {
            return null;
        }
        return (usl - mean) / (3 * shortTermStandardDeviation);
    }

    /**
     * 计算直方图 Cpl
     * (均值-LSL)/(3*短期标准差)
     * <p>
     * 如果没有下公差，则Cpl的值为Null
     *
     * @param lsl                        公差下限
     * @param mean                       均值
     * @param shortTermStandardDeviation 短期标准差
     */
    public static Double getCpl(Double lsl, Double mean, Double shortTermStandardDeviation) {
        if (lsl == null || shortTermStandardDeviation == 0d) {
            return null;
        }
        return (mean - lsl) / (3 * shortTermStandardDeviation);
    }

    /**
     * 计算直方图 Cpm
     * (USL-LSL)/Sqrt(Power(短期标准差,2)+Power(均值-目标值),2)
     * <p>
     * 两种情况下Cpm的值为Null
     * 1. 分母为0
     * 2. 没有USL，LSL和目标值这三个参数中的任何一种
     *
     * @param usl                        公差上限
     * @param lsl                        公差下限
     * @param mean                       均值
     * @param tar                        目标值
     * @param shortTermStandardDeviation 短期标准差
     */
    public static Double getCpm(Double usl, Double lsl, Double mean, Double tar, Double shortTermStandardDeviation) {
        if (usl == null || lsl == null || tar == null) {
            return null;
        }
        double denominator = Math.pow(shortTermStandardDeviation, 2) + Math.pow(mean - tar, 2);
        if (denominator == 0d) {
            return null;
        }
        return (usl - lsl) / (6 * Math.sqrt(denominator));
    }

    /**
     * 计算直方图 SIGL(短期)
     * 3*Cpk
     *
     * @param cpk Cpk
     */
    public static Double getSiglShortTerm(Double cpk) {
        if (cpk == null) {
            return null;
        }
        return 3 * cpk;
    }


    /**
     * 计算直方图 Ppk
     * <p>
     * 1. {(USL-均值)/(3*长期标准差), (均值-LSL)/(3*长期标准差)}中最小值
     * 2. (USL-均值)/(3*长期标准差)
     * 3. (均值-LSL)/(3*长期标准差)
     * <p>
     * 1. 双边公差使用公式一
     * 2. 单边上限，即只有上公差的时候，使用公式二
     * 3. 单边下限，即只有下公差的时候，使用公式三
     *
     * @param usl                       公差上限
     * @param lsl                       公差下限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getPpk(Double usl, Double lsl, Double mean, Double longTermStandardDeviation) {
        if (longTermStandardDeviation == 0d) {
            return null;
        }
        if (usl != null && lsl != null) {
            return Math.min((usl - mean) / (3 * longTermStandardDeviation), (mean - lsl) / (3 * longTermStandardDeviation));
        } else if (usl != null) {
            return (usl - mean) / (3 * longTermStandardDeviation);
        } else if (lsl != null) {
            return (mean - lsl) / (3 * longTermStandardDeviation);
        }
        return null;
    }

    /**
     * 计算直方图 Ppu
     * (USL-均值)/(3*长期标准差)
     * <p>
     * 如果没有上公差，则Ppu的值为Null
     *
     * @param usl                       公差上限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getPpu(Double usl, Double mean, Double longTermStandardDeviation) {
        if (usl == null || longTermStandardDeviation == 0d) {
            return null;
        }
        return (usl - mean) / (3 * longTermStandardDeviation);
    }

    /**
     * 计算直方图 Ppl
     * (均值-LSL)/(3*长期标准差)
     * <p>
     * 如果没有下公差，则Ppl的值为Null
     *
     * @param lsl                       公差下限
     * @param mean                      均值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getPpl(Double lsl, Double mean, Double longTermStandardDeviation) {
        if (lsl == null || longTermStandardDeviation == 0d) {
            return null;
        }
        return (mean - lsl) / (3 * longTermStandardDeviation);
    }

    /**
     * 计算直方图 Ppm
     * (USL-LSL)/Sqrt(Power(长期标准差,2)+Power(均值-目标值),2)
     * <p>
     * 两种情况下Ppm的值为Null
     * 1. 分母为0
     * 2. 没有USL，LSL和目标值这三个参数中的任何一种
     *
     * @param usl                       公差上限
     * @param lsl                       公差下限
     * @param mean                      均值
     * @param tar                       目标值
     * @param longTermStandardDeviation 长期标准差
     */
    public static Double getPpm(Double usl, Double lsl, Double mean, Double tar, Double longTermStandardDeviation) {
        if (usl == null || lsl == null || tar == null) {
            return null;
        }
        double denominator = Math.pow(longTermStandardDeviation, 2) + Math.pow(mean - tar, 2);
        if (denominator == 0d) {
            return null;
        }
        return (usl - lsl) / (6 * Math.sqrt(denominator));
    }

    /**
     * 计算直方图 SIGL(长期)
     * 3*Ppk
     *
     * @param ppk Ppk
     */
    public static Double getSiglLongTerm(Double ppk) {
        if (ppk == null) {
            return null;
        }
        return 3 * ppk;
    }

    /**
     * 分组数量 Sqrt(n)取整。其中n为数据集中数据数量  向上取整
     *
     * @param size 数据集中数据数量
     */
    public static Integer getBinNum(int size) {
        return (int) Math.ceil(Math.sqrt(size));
    }

    /**
     * 组距  (Max(数据集)-Min(数据集))/分组数量
     *
     * @param max         数据集最大值
     * @param min         数据集最小值
     * @param subgroupNum 分组数量
     */
    public static Double getGroupInterval(Double max, Double min, Integer subgroupNum) {
        return (max - min) / subgroupNum;
    }

    /**
     * 每个分组的起始区间(后续区间前端分)
     * 第一组：起始值 Min(数据集)-(组距/2)  结束值：起始值+组距
     * 第二组：起始值 第一组结束值            结束值：起始值+组距
     * 后续依此类推
     */
    public static Double getGroupStart(Double min, Double groupInterval) {
        return min - (groupInterval / 2);
    }


//    /**
//     * 正态分布拟合曲线(默认取100个)
//     * @param min 数据集最小值
//     * @param max 数据集最大值
//     * @param pointNum 拟合曲线每个数据点的序号，从0开始
//     * @param mean 均值
//     * @param sd 标准差
//     * @return
//     */
//    public static double[][] getNormalDistributionCurve(Double min, Double max, Integer pointNum, Double mean, Double sd) {
//        NormalDistribution normalDistribution = new NormalDistribution(mean, sd);
//        double[][] doubles = new double[pointNum][];
//        BigDecimal minB = BigDecimal.valueOf(min);
//        BigDecimal v = BigDecimal.valueOf(max).subtract(minB)
//                .divide(BigDecimal.valueOf(pointNum), 10, RoundingMode.DOWN);
//        for (int i = 0; i < pointNum; i++) {
//            double x = minB.add(BigDecimal.valueOf(i).multiply(v))
//                    .setScale(10, RoundingMode.DOWN)
//                    .doubleValue();
//            // 使用 density 获取对称的正态分布曲线
//            double y = BigDecimal.valueOf(normalDistribution.density(x))
//                    .setScale(10, RoundingMode.DOWN)
//                    .doubleValue();
//            doubles[i] = new double[]{x, y * 100d};
//        }
//        return doubles;
//    }



    /**
     * 正态分布拟合曲线(默认取100个)
     * NormalDistribution.density(Min+i*(Max-Min)/100)
     * i：拟合曲线每个数据点的序号，从0开始
     * Max：数据集最大值
     * Min：数据集最小值
     */
    public static double[][] getNormalDistributionCurve(Double min, Double max, Integer pointNum, Double mean, Double sd) {
        NormalDistribution normalDistribution = new NormalDistribution(mean, sd);
        Map<String, Double> map = new LinkedHashMap<>();
        double[][] doubles = new double[pointNum][];
        List<List<Double>> arrayList = new ArrayList<>();
        BigDecimal minB = BigDecimal.valueOf(min);
        BigDecimal v = BigDecimal.valueOf(max).subtract(minB).divide(BigDecimal.valueOf(pointNum), 10, RoundingMode.DOWN);
        for (int i = 0; i < pointNum; i++) {
            double x = minB.add(BigDecimal.valueOf(i).multiply(v)).setScale(10, RoundingMode.DOWN).doubleValue();
            double y = BigDecimal.valueOf(normalDistribution.cumulativeProbability(x)).setScale(10, RoundingMode.DOWN).doubleValue();
            if (x >= mean) {
                y = 1 - y;
            }

            /*添加二维数组*/

            double[] doub = new double[2];
            doub[0] = x;
            doub[1] = y * 100d;
            doubles[i] = doub;
        }
        return doubles;
    }



    /**
     * 核密度曲线
     */
    public static Map<Double, Double> getNuclearDensityCurve(List<Double> doubles, Double min, Double max, Double mean) {
        Map<Double, Double> map = new HashMap<>();
        double sumSquaredDifferences = 0.0;
        for (double value : doubles) {
            sumSquaredDifferences += Math.pow(value - mean, 2);
        }
        double stdDev = Math.sqrt(sumSquaredDifferences / doubles.size());
        double bandwidth = 1.06 * stdDev * Math.pow(doubles.size(), -0.2);

        for (double x = min; x <= max; x += 0.1) {
            double sum = 0.0;
            for (double xi : doubles) {
                double v = (x - xi) / bandwidth;
                double gaussianKernel = (1 / Math.sqrt(2 * Math.PI)) * Math.exp(-0.5 * v * v);
                sum += gaussianKernel;
            }
            double y = sum / (doubles.size() * bandwidth);
            map.put(x, y);
        }
        return map;
    }

    /**
     * 控制限或控制限反推
     *
     * @param controlLimitDTO 反推数据
     * @param type            1:控制限反推 2:双边公差限反推 3:单边公差限反推
     * @param testType        测试类型 如果为缺陷或不良就只算均值
     */
    public static void backstepping(ControlLimitDTO controlLimitDTO, Integer type, Integer testType) {
        Double mean = null;
        Double F_SP = null;
        if (type == 1) {
            /*根据控制限反推*/
            /*F_MEAN = (UCL+LCL)/2*/
            mean = (controlLimitDTO.getUCL() + controlLimitDTO.getLCL()) / 2;

            /*UCL = F_MEAN+3*F_SP/Sqrt(n)*/
            if (!testType.equals(TEST_INF_TYPEEnum.DEFECT.getType()) && !testType.equals(TEST_INF_TYPEEnum.DEFECTIVE.getType()))
                F_SP = ((controlLimitDTO.getUCL() - mean) * Math.sqrt(controlLimitDTO.getN())) / 3;

        } else if (type == 2) {
            /*根据双边公差反推*/
            mean = (controlLimitDTO.getSpecInfDto().getF_USL() + controlLimitDTO.getSpecInfDto().getF_LSL()) / 2;
            /*(USL-LSL)/(6*短期标准差)*/
            if (!testType.equals(TEST_INF_TYPEEnum.DEFECT.getType()) && !testType.equals(TEST_INF_TYPEEnum.DEFECTIVE.getType()))
                F_SP = (controlLimitDTO.getSpecInfDto().getF_USL() - controlLimitDTO.getSpecInfDto().getF_LSL()) /
                        (6 * controlLimitDTO.getSpecInfDto().getF_CP());

        } else {
            /*根据单边公差反推*/
            Double b = Math.abs(controlLimitDTO.getSpecInfDto().getF_USL() - controlLimitDTO.getF_MEAN());

            mean = controlLimitDTO.getF_MEAN();
            if (!testType.equals(TEST_INF_TYPEEnum.DEFECT.getType()) && !testType.equals(TEST_INF_TYPEEnum.DEFECTIVE.getType()))
                F_SP = b / (3 * controlLimitDTO.getSpecInfDto().getF_CP());
        }
        controlLimitDTO.setF_MEAN(mean);
        controlLimitDTO.setF_SP(F_SP);
    }
}
