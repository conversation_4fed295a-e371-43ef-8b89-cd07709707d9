package com.yingfei.entity.util.controlChart;

import com.yingfei.entity.dto.ControlLimitDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import com.yingfei.entity.dto.SubgroupDataDTO;
import com.yingfei.entity.enums.ControlChartTypeEnum;
import com.yingfei.entity.util.ControlChartCalculateService;
import com.yingfei.entity.vo.DataPointVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 控制图分组类型-------->P图
 */
@Service
public class ControlChartGroupNP implements ControlChartCalculateService {


    /**
     * 图类P图-------> 数据点计算公式为: 子组内测试值/样本量
     *
     * @param subgroupDataDTOList   子组测试数据,要按子组时间正序排列
     * @param chartTypeEnum 图类型
     */
    @Override
    public List<DataPointVO> dataPoint(List<SubgroupDataDTO> subgroupDataDTOList, ControlChartTypeEnum chartTypeEnum) {
        List<DataPointVO> list = new ArrayList<>();
        subgroupDataDTOList.forEach(subgroupDataDTO -> {
            /*将F_VAL的值求和*/
            Double aDouble = subgroupDataDTO.getSgrpValDto().getSgrpValChildDto().getSum();
            list.add(DataPointVO.getDataPoint(subgroupDataDTO, aDouble ));
        });
        return list;
    }

    /**
     * CL=Mean*N
     * Mean=不良率
     * @return
     */
    @Override
    public Double controlLimitCL(ControlLimitDTO controlLimitDto) {
        return controlLimitDto.getF_MEAN()*controlLimitDto.getN();
    }

    /**
     * CL + 3*sqrt(CL*(1-Mean))
     * 如果实际UCL大于N，则返回N
     * @return
     */
    @Override
    public Double controlLimitUCL(ControlLimitDTO controlLimitDto) {
        double cl = controlLimitDto.getF_MEAN()*controlLimitDto.getN();
        double ucl = cl + 3 * Math.sqrt(cl*(1-controlLimitDto.getF_MEAN()));
        return ucl>controlLimitDto.getN()?controlLimitDto.getN():ucl;
    }

    /**
     * 0
     */
    @Override
    public Double controlLimitLCL(ControlLimitDTO controlLimitDto) {
        double cl = controlLimitDto.getF_MEAN()*controlLimitDto.getN();
        double lcl = cl - 3 * Math.sqrt(cl*(1-controlLimitDto.getF_MEAN()));
        return lcl < 0 ? 0 : lcl;
    }

    @Override
    public Double historyControlLimitCL(ControlLimitDTO controlLimitDto) {
        return null;
    }

    @Override
    public Double historyControlLimitUCL(ControlLimitDTO controlLimitDto) {
        return null;
    }

    @Override
    public Double historyControlLimitLCL(ControlLimitDTO controlLimitDto) {
        return null;
    }
}
