package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.PART_REV;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 储存产品版本信息表
 *
 * @TableName PART_REV
 */
@Data
public class PART_REV_VO extends BaseEntity {

    /**
     * 记录主键
     */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PTRV;
    /**
     * 关联的产品ID
     */
    @ApiModelProperty("关联的产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
     * 版本号
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("版本号")
    @Length(max = 50, message = "编码长度不能超过50")
    private String F_NAME;
    /**
     * 生效时间，如果不指定则为创建时间
     */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("生效时间，如果不指定则为创建时间")
    private Date F_STTM;
    /**
     * 结束时间，如果不指定则为2099-1-1
     */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("结束时间，如果不指定则为2099-1-1")
    private Date F_FNTM;
    /**
     * 是否删除标记，默认值为0
     */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 产品和版本对应的公差限
     */
    private List<SPEC_INF_VO> specInfVoList;

    /**
     * 产品和版本对应的产品测试
     */
    private List<PART_TEST_INF_VO> partTestInfVoList;

    public static PART_REV initPartRev(PART_INF_VO partInfVo) {
        PART_REV partRev = new PART_REV();
        partRev.setF_PART(partInfVo.getF_PART());
        partRev.setF_STTM(partInfVo.getPartRevStartTime() == null ? DateUtils.getNowDate() : partInfVo.getPartRevStartTime());
        partRev.setF_FNTM(partInfVo.getPartRevEndTime() == null ? DateUtils.parseDate("2099-01-01") : partInfVo.getPartRevEndTime());
        partRev.setF_CRUE(partInfVo.getF_CRUE());
        partRev.setF_EDUE(partInfVo.getF_EDUE());
        partRev.setF_NAME(partInfVo.getPartRevName());
        return partRev;
    }
}
