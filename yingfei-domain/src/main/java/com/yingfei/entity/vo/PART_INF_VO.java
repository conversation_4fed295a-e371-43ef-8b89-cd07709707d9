package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.annotation.Excel;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.dto.TAG_DAT_DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 产品信息表
 *
 * @TableName PART_INF
 */
@Data
@Accessors(chain = true)
public class PART_INF_VO extends BaseEntity {

    /**
     * 记录主键
     */
    @NotNull(message = "[记录主键]不能为空")
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
     * 工厂ID（产品归属于工厂这一层级）
     */
    @NotNull(message = "[工厂ID（产品归属于工厂这一层级）]不能为空")
    @ApiModelProperty("工厂ID（产品归属于工厂这一层级）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;

    /**
     * 产品名称
     */
    @NotBlank(message = "[产品名称]不能为空")
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称(必填)")
    @Length(max = 100, message = "编码长度不能超过100")
    private String F_NAME;
    /**
     * 产品详细名称
     */
    @NotBlank(message = "[产品详细名称]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("产品详细名称")
    @Excel(name = "产品详细名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String F_LONG_NAME;

    @ApiModelProperty("工厂名称")
    @Excel(name = "工厂名称(必填)")
    private String F_PLNT_NAME;

    /**
     * 产品图片地址
     */
    @NotBlank(message = "[产品图片地址]不能为空")
    @Size(max = 255, message = "编码长度不能超过255")
    @ApiModelProperty("产品图片地址")
    @Length(max = 255, message = "编码长度不能超过255")
    private String F_IMAGE;
    /**
     * 因子，默认为1
     */
    @NotNull(message = "[因子，默认为1]不能为空")
    @ApiModelProperty("因子，默认为1")
    @Excel(name = "因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
     * 是否删除标记，默认值为0
     */
    @NotNull(message = "[是否删除标记，默认值为0]不能为空")
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
     * 记录创建用户ID
     */
    @NotNull(message = "[记录创建用户ID]不能为空")
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @NotNull(message = "[记录编辑用户ID]不能为空")
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    @ApiModelProperty("标签数量")
    private Integer tagCount;


    @ApiModelProperty("版本号状态(每个产品会关联一个或多个版本号，" +
            "每个版本会有起始时间。如果当前系统时间在起始时间内，" +
            "则为生效；在之外，则是失效；因此，状态有三个：生效 0、失效 1和所有 2)")
    private Integer status = 2;

    @ApiModelProperty("产品ID集合")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> partIds;

    @ApiModelProperty("版本ID集合")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> partRevIds;

    @ApiModelProperty("版本名称")
    @Excel(name = "版本名称")
    private String partRevName;

    @ApiModelProperty("版本开始时间")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @Excel(name = "版本开始时间(格式:yyyy/MM/dd 选填)", width = 50)
    private Date partRevStartTime;

    @ApiModelProperty("版本结束时间(选填)")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @Excel(name = "版本结束时间(格式:yyyy/MM/dd 选填)", width = 50)
    private Date partRevEndTime;

    @ApiModelProperty("是否展开版本(0:展开,1:关闭)")
    private Integer unfold = 0;

    @ApiModelProperty("所选标签列表")
    private List<TAG_DAT_DTO> tagDatDtoList;

    @ApiModelProperty("批量修改标签状态(0:删除 1:添加 2:修改)")
    private Integer tagEdType;

    @Excel(name = "覆盖或恢复(重名默认覆盖 0 覆盖 1 恢复)", width = 30)
    private Integer coverOrRecover = 0;

    @ApiModelProperty("版本id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long partRevId;

    /**
     * 标签组id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tggpId;

    /**
     * 标签id列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> tagIds;

    /**
     * 关闭版本时间
     */
    @ApiModelProperty("关闭版本时间")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date closeTime;

    /**
     * 数据隔离字段
     */
    @ApiModelProperty("数据隔离字段")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> hierarchyInfIds;

    /**
     * 参数集层级列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> parameterHierIds;

    /**
     * 删除产品或版本
     */
    private Map<Long, List<Long>> delMap;
}
