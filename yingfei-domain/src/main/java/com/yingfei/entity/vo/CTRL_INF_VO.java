package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
* 储存控制限表
* @TableName CTRL_INF
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CTRL_INF_VO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CTRL;
    /**
    * 产品ID
    */
    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
    * 测试ID
    */
    @ApiModelProperty("测试ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;
    /**
    * 过程ID
    */
    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
    * 生效日期
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("生效日期")
    private Date F_EFTM;
    /**
    * 过程均值
    */
    @ApiModelProperty("过程均值")
    private Double F_MEAN;
    /**
    * 过程西格玛
    */
    @ApiModelProperty("过程西格玛")
    private Double F_SP = 0d;
    /**
    * 公差下限
    */
    @ApiModelProperty("公差下限")
    private Double F_SPL = 0d;
    /**
    * 合理上限
    */
    @ApiModelProperty("合理上限")
    private Double F_SW = 0d;
    /**
    * 合理下限
    */
    @ApiModelProperty("合理下限")
    private Double F_SWL = 0d;
    /**
    * 因子，默认为1
    */
    @ApiModelProperty("因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
    * 处理方式模板对应ID
    */
    @ApiModelProperty("处理方式模板对应ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PSTP;
    /**
    * 报警规则模板对应ID
    */
    @ApiModelProperty("报警规则模板对应ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ARTP;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录最后编辑用户ID
    */
    @ApiModelProperty("记录最后编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 1.单值-移动极差,  备注: 单值是图表1  移动极差是图表2
     * 2.单值-移动极差-极差件内,
     * 3.单值-移动极差-标准差件内,
     * 4.均值-极差,
     * 5.均值-极差-极差件内,
     * 6.均值-极差-标准差件内,
     * 7.均值-标准差,
     * 8.均值-标准差-极差件内,
     * 9.均值-标准差-标准差件内,
     * 10.U图,
     * 11.C图
     * 12.P图
     * 13.NP图
     * 14.LaneyP图
     * 15.LaneyU图
     */
    private Integer F_CHART_TYPE = 4;

    /**
     * 产品版本id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PTRV;

    /**
     * 控制限id列表
     */
    private List<String> ids;

    /**
     * 产品id列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> partIds;

    /**
     * 版本id列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> ptrvIds;

    /**
     * 过程id列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> prcsIds;

    /**
     * 测试id列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> testIds;

    /**
     * 数据隔离字段
     */
    @ApiModelProperty("数据隔离字段")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> hierarchyInfIds;

    public CTRL_INF_VO(Long f_PART, Long f_PTRV, Long f_PRCS, Long f_TEST) {
        F_PART = f_PART;
        F_PTRV = f_PTRV;
        F_TEST = f_TEST;
        F_PRCS = f_PRCS;
    }
}
