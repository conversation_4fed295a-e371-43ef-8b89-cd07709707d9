package com.yingfei.entity.vo.dataCollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.dto.autoCollect.DataFileAutoCollectConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数据采集配置类
 */
@Data
@ApiModel
public class DataCollectionVO {

    /*-------------------第1步-----------------*/
    /**
     * 采集类型(1:子组 2:公差限)
     */
    @ApiModelProperty("采集类型(1:子组 2:公差限)")
    private Integer collectType = 1;

    /**
     * 任务类型(1:数据文件  2:数据库)
     */
    @ApiModelProperty("任务类型(1:数据文件  2:数据库)")
    private Integer status;

    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    private String name;

    /**
     * 任务类型为数据文件时指定文件路径
     */
    @ApiModelProperty("任务类型为数据文件时指定文件路径")
    private String filePath;

    /**
     * 文件通配符
     */
    @ApiModelProperty("文件通配符")
    private String wildcardPattern = "*";

    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;


    /*-------------------第2,3步-----------------*/
    /**
     * 解析规则id
     */
    @ApiModelProperty("解析规则id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dataImportId;

    /**
     * 数据库配置id
     */
    @ApiModelProperty("数据库配置id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long dbConfigId;

    /**
     * 字段映射加保存条件
     */
    private DataFileAutoCollectConfigDTO dataFileAutoCollectConfigDTO;

    /**
     * 处理SQL
     */
    @ApiModelProperty("处理SQL")
    private String handleSql;

    /**
     * 是否启用启动sql(0:否 1:是)
     */
    private Integer startType = 0;

    /**
     * 启动SQL
     */
    @ApiModelProperty("启动SQL")
    private String startSql;

    @ApiModelProperty("字段列表")
    List<String> filedList;


    /*-------------------第4步-----------------*/
    /**
     * 任务开始时间
     */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(
//            pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
//            timezone = "UTC"
//    )
    @ApiModelProperty("任务开始时间")
    private Date F_START_TIME;
    /**
     * 时间间隔
     */
    @ApiModelProperty("时间间隔")
    private Integer F_TIME_INTERVAL;
    /**
     * 时间类型
     * @see com.yingfei.entity.enums.TimeEnum
     */
    @ApiModelProperty("时间类型")
    private Integer F_TIME_TYPE;

    /**
     * 失败通知类型
     * @see com.yingfei.entity.enums.NOTIFICATION_TYPEEnum
     */
    @ApiModelProperty("失败通知类型")
    private Integer sendType;

    /**
     * 通知人
     */
    @ApiModelProperty("通知人")
    private List<String> emplIds;


    /*-------------------公差限采集字段-----------------*/
    /**
     * 公差限一样覆盖或跳过(0:否 1:是)
     */
    private Integer specSkip;
}
