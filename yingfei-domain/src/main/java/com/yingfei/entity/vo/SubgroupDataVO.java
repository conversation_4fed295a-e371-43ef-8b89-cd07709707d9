package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.SGRP_CMT;
import com.yingfei.entity.domain.SGRP_DSC;
import com.yingfei.entity.dto.CalculatedControlLimit;
import com.yingfei.entity.dto.ReturnNameDTO;
import com.yingfei.entity.dto.SGRP_VAL_CHILD_DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 子组主信息表
 *
 * @TableName SGRP_INF
 */
@Data
public class SubgroupDataVO extends ReturnNameDTO implements Serializable {

    /**
     * 记录主键
     */
    @NotNull(message = "[记录主键]不能为空")
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;
    /**
     * 工艺流程ID
     */
    @NotNull(message = "[工艺流程ID]不能为空")
    @ApiModelProperty("工艺流程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFPS;
    /**
     * 工艺节点ID
     */
    @NotNull(message = "[工艺节点ID]不能为空")
    @ApiModelProperty("工艺节点ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_MFND;

    /**
     * 产品ID
     */
    @NotNull(message = "[产品ID]不能为空")
    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
     * 过程ID
     */
    @NotNull(message = "[过程ID]不能为空")
    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
     * 产品版本ID
     */
    @NotNull(message = "[产品版本ID]不能为空")
    @ApiModelProperty("产品版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_REV;
    /**
     * 产品批次
     */
    @NotNull(message = "[产品批次]不能为空")
    @ApiModelProperty("产品批次")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_LOT;
    /**
     * 工作ID
     */
    @NotNull(message = "[工作ID]不能为空")
    @ApiModelProperty("工作ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JOB;

    /**
     * 工作组ID
     */
    @ApiModelProperty("工作组ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JBGP;
    /**
     * 班次ID
     */
    @NotNull(message = "[班次ID]不能为空")
    @ApiModelProperty("班次ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SHIFT;

    @ApiModelProperty("班次组ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SHGP;
    /**
     * 子组样本量
     */
    @NotNull(message = "[子组样本量]不能为空")
    @ApiModelProperty("子组样本量")
    private Integer F_SGSZ;
    /**
     * 子组时间
     */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("子组时间")
    private Date F_SGTM;
    /**
     * 失效标识 0=激活，1=失效
     */
    @NotNull(message = "[失效标识 0=激活，1=失效]不能为空")
    @ApiModelProperty("失效标识 0=激活，1=失效")
    private Integer F_FLAG;
    /**
     * 是否删除标记，默认值为0
     */
    @NotNull(message = "[是否删除标记，默认值为0]不能为空")
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
     * 创建用户
     */
    @NotNull(message = "[创建用户]不能为空")
    @ApiModelProperty("创建用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 编辑用户
     */
    @NotNull(message = "[编辑用户]不能为空")
    @ApiModelProperty("编辑用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;


    /**
     * 子计划数量
     */
    private Integer F_NUM;

    /**
     * 检查计划id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_INSP_PLAN;

    /**
     * 过程列表(多过程使用) 前端传入
     */
    @ApiModelProperty("过程列表(多过程使用) 前端传入")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> prcsList;

    /**
     * 测试对象列表
     */
    @ApiModelProperty("测试对象列表")
    private List<SGRP_VAL_CHILD_DTO> sgrpValChildDtoList;

    /**
     * 单个测试对象
     */
    @ApiModelProperty("单个测试对象")
    private SGRP_VAL_CHILD_DTO sgrpValChildDto;

    /**
     * 描述符列表
     */
    @ApiModelProperty("描述符列表")
    private List<SGRP_DSC> sgrpDscList;

    /**
     * 子组备注列表
     */
    @ApiModelProperty("子组备注列表")
    private List<SGRP_CMT> sgrpCmtList;

    /**
     * 控制限信息
     */
    @ApiModelProperty("控制限信息")
    List<CalculatedControlLimit> calculatedControlLimits;

    /**
     * 抽样唯一标识
     */
    private String F_SAMPLE_ID;

    /**
     * 子计划是否完成状态(0:未完成 1:已完成)
     */
    private Integer F_FINISH_STATUS = 1;

    /**
     * 子组缓存处理状态(0:待处理,1:正在处理)
     */
    private Integer F_CACHE_STATUS = 0;

    /**
     * 子计划顺序(前端用)
     */
    private Integer order;

    /**
     * 工厂id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PLNT;

    /**
     * 是否历史数据(0:否 1:是)
     */
    private Integer historicalData = 0;

    /**
     * 检查计划中子计划id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long childId;
}
