package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.PARAMETER_EMPL_LINK;
import com.yingfei.entity.dto.PARAMETER_CHILD_DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
* 参数集表
* @TableName PARAMETER_SET_INF
*/
@Data
public class PARAMETER_SET_INF_VO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRST;
    /**
    * 参数集名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("参数集名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;

    /**
     * 筛选条件list
     */
    @ApiModelProperty("筛选条件list")
    private List<PARAMETER_CHILD_DTO> parameterChildDtoList;

    /**
    * 时间窗口类型。1=动态 2=静态
    */
    @ApiModelProperty("时间窗口类型。1=动态 2=静态")
    private Integer F_TIME_WINDOW_TYPE = 1;
    /**
    * 日期范围类型。1=分钟，2=小时，3=天，4=周，5=月，6=年
    */
    @ApiModelProperty("日期范围类型。1=分钟，2=小时，3=天，4=周，5=月，6=年")
    private Integer F_DATERANGE_TYPE = 3;
    /**
    * 日期范围值
    */
    @ApiModelProperty("日期范围值")
    private Integer F_RANGE_INTERVAL;
    /**
    * 开始时间（仅当选择静态窗口时使用此值）
    */
    @ApiModelProperty("开始时间（仅当选择静态窗口时使用此值）")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date F_START_DATE;
    /**
    * 结束时间（仅当选择静态窗口时使用此值）
    */
    @ApiModelProperty("结束时间（仅当选择静态窗口时使用此值）")
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date F_END_DATE;
    /**
    * 最大返回子组数
    */
    @ApiModelProperty("最大返回子组数")
    private Integer F_MAX_ITEM =1000;
    /**
    * 是否包含失效的子组 1=不包含，0=包含，默认值为1
    */
    @ApiModelProperty("是否包含失效的子组 1=不包含，0=包含，默认值为1")
    private Integer F_EXCLUDE_DISABLED_SGS = 1;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录最后编辑用户ID
    */
    @ApiModelProperty("记录最后编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 参数集id列表
     */
    private List<String> ids;

    /**
     * 排除参数集id列表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> notIds;

    /**
     * 参数集可见范围
     */
    private List<PARAMETER_EMPL_LINK> parameterEmplLinkList;

    /**
     * 数据隔离字段
     */
    @ApiModelProperty("数据隔离字段")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> hierarchyInfIds;
}
