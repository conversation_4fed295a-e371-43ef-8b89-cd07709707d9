package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
* 储存工单信息表
* @TableName JOB_DAT
*/
@Data
public class JOB_DAT_VO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JOB;
    /**
    * 工单所关联的工单组ID
    */
    @ApiModelProperty("工单所关联的工单组ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_JBGP;
    /**
    * 工单名称
    */
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("工单名称")
    @Length(max= 100,message="编码长度不能超过100")
    private String F_NAME;
    /**
    * 工单因子，默认为1
    */
    @ApiModelProperty("工单因子，默认为1")
    private Double F_FACTOR = 1D;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 工单开放使用时间按
     */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date F_RELEASE_TIME;

    /**
     * 工单关闭使用时间按
     */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date F_CLOSE_TIME;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> ids;

    /**
     * 是否自动创建(0:否 1:是)
     * 前端检查模板手动输入时填写1
     */
    @ApiModelProperty("是否自动创建(0:否 1:是) 前端检查模板手动输入时填写1")
    private Integer isAutoCreate = 0;
}
