package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * 用户登录日志
 * @TableName ACCESS_LOG_INF
 */
@Data
@ApiModel
public class ACCESS_LOG_INF_VO extends BaseEntity {
    /**
     * 记录主键
     */
    private String F_LLOG;

    /**
     * 登录账号
     */
    private String F_CODE;

    /**
     * 状态 0成功 1失败
     */
    private Integer F_STATUS;

    /**
     * ip地址
     */
    private String F_IPADDR;

    /**
     * 描述
     */
    private String F_MSG;

    /**
     * 访问时间
     */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    private Date F_ACTM;



}
