package com.yingfei.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.EVNT_ATTACHMENT_INF;
import com.yingfei.entity.domain.EVNT_COMMENT_INF;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
* 报警信息表
* @TableName EVNT_INF
*/
@Data
@Accessors(chain = true)
public class EVNT_INF_VO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EVNT;
    /**
    * 事件类型
    */
    @ApiModelProperty("事件类型")
    private Integer F_TYPE;
    /**
    * 事件名称
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("事件名称")
    @Length(max= 255,message="编码长度不能超过255")
    private String F_NAME;
    /**
    * 产品ID
    */
    @ApiModelProperty("产品ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;
    /**
    * 过程ID
    */
    @ApiModelProperty("过程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;
    /**
    * 测试ID
    */
    @ApiModelProperty("测试ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;
    /**
    * 报警发生时间
    */
    @ApiModelProperty("报警发生时间")
    private Date F_EVTM;
    /**
    * 报警对应的子组时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("报警对应的子组时间")
    private Date F_SGTM;
    /**
    * 事件对应的子组ID。如果事件类型与子组无关，或子组已不存存在（例如，删除子组），则值为0
    */
    @ApiModelProperty("事件对应的子组ID。如果事件类型与子组无关，或子组已不存存在（例如，删除子组），则值为0")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_SGRP;
    /**
    * 异常原因ID
    */
    @ApiModelProperty("异常原因ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RTCS;
    /**
    * 异常原因时间
    */
    @ApiModelProperty("异常原因时间")
    private Date F_RCTM;
    /**
    * 改善措施ID
    */
    @ApiModelProperty("改善措施ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_RSAT;
    /**
    * 改善措施时间
    */
    @ApiModelProperty("改善措施时间")
    private Date F_RSTM;
    /**
    * 是否删除标记，默认值为0
    */
    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = YesOrNoEnum.NO.getType();
    /**
    * 记录创建用户ID
    */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
    * 记录编辑用户ID
    */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;

    /**
     * 解决状态(0:未解决 1:已解决)
     */
    private Integer F_STATUS;

    @ApiModelProperty("报警信息备注列表")
    private List<EVNT_COMMENT_INF> evntCommentInfList;

    @ApiModelProperty("报警信息附件列表")
    private List<EVNT_ATTACHMENT_INF> evntAttachmentInfList;

    @ApiModelProperty("子组id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> sgrpInfList;

    @ApiModelProperty("产品id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> partList;

    @ApiModelProperty("过程id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> prcsList;

    @ApiModelProperty("测试id列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> testList;

    private String partName;

    private String prcsName;

    private String testName;

    /**
     * 是否用临时表(0否 1是)
     */
    private Integer isTemp = 0;

    /**
     * 临时表标识
     */
    private String tempIdentify;
}
