package com.yingfei.entity.vo.dataCollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.domain.DB_CONFIG_INF;
import com.yingfei.entity.dto.TaskScheduleModel;
import com.yingfei.entity.dto.autoCollect.DbDcsConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
* 自动采集任务配置
* @TableName DC_JOB_INF
*/
@Data
public class DC_JOB_INF_VO extends BaseEntity {

    /**
    * 记录主键
    */
    @ApiModelProperty("记录主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DCJB;
    /**
    * 名称
    */
    @ApiModelProperty("名称")
    private String F_NAME;
    /**
    * 数据库地址
    */
    @ApiModelProperty("数据库地址")
    private String F_URL;
    /**
    * 数据库账号
    */
    @ApiModelProperty("数据库账号")
    private String F_USERNAME;
    /**
    * 数据库密码
    */
    @ApiModelProperty("数据库密码")
    private String F_PASSWORD;
    /**
    * 数据库配置表主键
    */
    @ApiModelProperty("数据库配置表主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_DBCO;
    /**
    * 任务开始时间
    */
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    @ApiModelProperty("任务开始时间")
    private Date F_START_TIME;
    /**
    * 时间间隔
    */
    @ApiModelProperty("时间间隔")
    private Integer F_TIME_INTERVAL;
    /**
    * 时间类型
    */
    @ApiModelProperty("时间类型")
    private Integer F_TIME_TYPE;
    /**
    * 处理SQL
    */
    @ApiModelProperty("处理SQL")
    private String F_SQL;
    /**
    * 启动SQL
    */
    @ApiModelProperty("启动SQL")
    private String F_START_SQL;

    /**
     * 所选库名
     */
    @ApiModelProperty("所选库名")
    private String F_DB_NAME;

    /**
     * 采集类型(1:数据库 2:文件)
     */
    @ApiModelProperty("采集类型(1:数据库 2:文件)")
    private Integer F_TYPE;

    /**
     * 文件连接配置json
     */
    @ApiModelProperty("文件连接配置json")
    private String F_FILE_CONFIG;

    /**
     * dcs配置json
     */
    @ApiModelProperty("dcs配置json")
    private String F_DCS_CONFIG;

    /**
     * 测试SQL
     */
    @ApiModelProperty("测试SQL")
    private String testSql;

    /**
     * 数据库配置
     */
    private DB_CONFIG_INF dbConfigInf;

    /**
     * dcs配置
     */
    private DbDcsConfigDTO dbDcsConfigDTO;

    /**
     * 高级配置
     */
    private TaskScheduleModel taskScheduleModel = new TaskScheduleModel();

    /**
     * 记录创建用户ID
     */
    @ApiModelProperty("记录创建用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_CRUE;
    /**
     * 记录编辑用户ID
     */
    @ApiModelProperty("记录编辑用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_EDUE;
}
