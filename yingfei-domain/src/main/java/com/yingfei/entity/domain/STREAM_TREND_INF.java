package com.yingfei.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yingfei.common.core.annotation.Excel;
import com.yingfei.common.core.utils.bean.BeanUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 能力趋势表
 */
@Data
@ApiModel
@Accessors(chain = true)
@TableName("STREAM_TREND_INF")
public class STREAM_TREND_INF extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_ID;

    @ApiModelProperty("产品表主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PART;

    @ApiModelProperty("产品版本表主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PTRV;

    @ApiModelProperty("过程表主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_PRCS;

    @ApiModelProperty("测试表主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long F_TEST;

    @ApiModelProperty("开始采集时间")
    
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date F_START;

    @ApiModelProperty("结束采集时间")
    
    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date F_END;

    @Excel(name = "CP")
    @ApiModelProperty("cp")
    private Double F_CP;

    @Excel(name = "CPK")
    @ApiModelProperty("cpk")
    private Double F_CPK;

    @ApiModelProperty("目标值")
    private Double F_TAR;

    @Excel(name = "目标CP")
    @ApiModelProperty("CP目标值(SPEC_LIM--F_CP)")
    private Double F_CPTAR;

    @Excel(name = "目标CPK")
    @ApiModelProperty("CPK目标值(SPEC_LIM--F_CPK)")
    private Double F_CPKTAR;

    @ApiModelProperty("极差的和")
    private Double F_RANGE_SUM;

    @ApiModelProperty("短期标准差的和")
    private Double F_SD_SUM;

    @ApiModelProperty("子组数量")
    private Integer F_SGRP_COUNT;

    @ApiModelProperty("测试值数量和")
    private Integer F_VALUE_COUNT;

    @ApiModelProperty("报警事件data数量")
    private Integer F_EVNT_COUNT = 0;

    @ApiModelProperty("报警事件data根据type拆开数量")
    private Integer F_EVNT_DETAIL_COUNT = 0;

    @ApiModelProperty("是否删除标记，默认值为0")
    private Integer F_DEL = 0;

    @ApiModelProperty("统计周期：0=按天统计，1=按月统计")
    private Integer F_TYPE = 0;

    public static STREAM_TREND_INF init() {
        STREAM_TREND_INF data = new STREAM_TREND_INF();
        BeanUtils.setAllFieldsToNull(data);
        return data;
    }
}
