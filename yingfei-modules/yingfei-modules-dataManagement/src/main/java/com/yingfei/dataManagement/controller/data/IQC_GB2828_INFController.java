package com.yingfei.dataManagement.controller.data;

import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.CreateUpdateByEnum;
import com.yingfei.common.core.exception.base.BaseException;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.poi.ExcelUtil;
import com.yingfei.common.core.web.controller.BaseController;
import com.yingfei.common.core.web.page.TableDataInfo;
import com.yingfei.common.security.annotation.CreateUpdateBy;
import com.yingfei.common.security.annotation.NotResubmit;
import com.yingfei.dataManagement.service.IQC_GB2828_INFService;
import com.yingfei.entity.domain.IQC_GB2828_INF;
import com.yingfei.entity.enums.SAMPLE_LEVELEnum;
import com.yingfei.entity.vo.excel.IQC_GB2828_EXCEL_VO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "IQC_GB2828信息API")
@RestController
@RequestMapping("/iqc")
public class IQC_GB2828_INFController extends BaseController {

    @Resource
    private IQC_GB2828_INFService iqcGb2828InfService;

    /**
     * 导入文件生成数据
     */
    @NotResubmit
    @ApiOperation("导入文件生成数据")
    @PostMapping("/importFile")
    public R<?> importFile(@RequestPart("file") MultipartFile file, Integer titleLine) {
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!suffix.equals("xlsx")) {
            throw new BaseException("PLEASE_CONVERT_TO_XLSX_FORMAT_FOR_IMPORT");
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<IQC_GB2828_EXCEL_VO> util = new ExcelUtil<>(IQC_GB2828_EXCEL_VO.class);
            List<IQC_GB2828_EXCEL_VO> iqcGb2828ExcelVoList = util.importExcel(inputStream, titleLine - 1);
            Map<String, String> map = iqcGb2828InfService.importFile(iqcGb2828ExcelVoList);
            return R.ok(map);
        } catch (Exception e) {
            log.error("导入IQC信息失败");
            e.printStackTrace();
        }
        return R.fail(I18nUtils.getMessage("IMPORT_FAILED"));
    }

    /**
     * 获取列表
     */
    @ApiOperation("获取列表")
    @PostMapping("/list")
    public TableDataInfo<?> list(@RequestBody IQC_GB2828_INF iqcGb2828Inf) {
        List<IQC_GB2828_INF> list = iqcGb2828InfService.getList(iqcGb2828Inf);
        TableDataInfo<?> dataTable = getDataTable(list);
        dataTable.setTotal(iqcGb2828InfService.getTotal(iqcGb2828Inf));
        return dataTable;
    }

    /**
     * 修改IQC数据
     */
    @CreateUpdateBy(businessType = CreateUpdateByEnum.UPDATE)
    @ApiOperation("修改IQC数据")
    @PostMapping("/update")
    public R<?> update(@RequestBody IQC_GB2828_INF iqcGb2828Inf) {
        iqcGb2828InfService.updateById(iqcGb2828Inf);
        return R.ok();
    }

    /**
     * 获取枚举
     */
    @ApiOperation("获取枚举")
    @GetMapping("/getMap")
    public R<?> getMap() {
        Map<Integer, String> map = SAMPLE_LEVELEnum.getMap();
        return R.ok(map);
    }
}
