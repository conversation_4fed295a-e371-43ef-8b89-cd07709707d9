package com.yingfei.dataManagement.controller.gauge;

import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.domain.R;
import com.yingfei.common.core.enums.YesOrNoEnum;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.redis.configure.RedisConstant;
import com.yingfei.common.redis.service.RedisService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.dataManagement.service.gauge.GAUGE_COMMONService;
import com.yingfei.entity.dto.GAUGE_AGENT_DTO;
import com.yingfei.entity.dto.GAUGE_CONNECTION_CONFIG_DTO;
import com.yingfei.entity.dto.GAUGE_CONNECTION_DTO;
import com.yingfei.entity.dto.SPEC_INF_DTO;
import com.yingfei.entity.vo.GAUGE_AGENT_VO;
import com.yingfei.entity.vo.SPEC_INF_VO;
import com.yingfei.entity.vo.SerialDebuggingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "量具外部调用信息API")
@RestController
@RequestMapping("/gauge_common")
public class GAUGE_COMMONController {

    @Resource
    private GAUGE_COMMONService gaugeCommonService;
    @Value("${spring.redis.host}")
    private String redisHost;
    @Value("${spring.redis.port}")
    private String redisPort;
    @Value("${spring.redis.password}")
    private String redisPassword;
    @Value("${spring.redis.database}")
    private String redisDatabase;
    @Resource
    private SPEC_INFService specInfService;
    @Resource
    private RedisService redisService;

    /**
     * 保存量具Agent名称
     */
    @ApiOperation("保存量具Agent名称")
    @PostMapping("/saveAgentName")
    public R<?> saveAgentName(@RequestBody GAUGE_AGENT_VO gaugeAgentVo) {
        try {
            return R.ok(gaugeCommonService.saveAgentName(gaugeAgentVo));
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail();
        }
    }

    /**
     * 根据硬件id获取量具Agent信息
     */
    @ApiOperation("根据硬件id获取量具Agent信息")
    @GetMapping("/findByAgentHardwareId")
    public R<?> findByHardwareId(String hardwareId) {
        GAUGE_AGENT_DTO gaugeAgentDto = gaugeCommonService.findByHardwareId(hardwareId);
        if (gaugeAgentDto != null) {
            return R.ok(gaugeAgentDto);
        }
        return R.fail();
    }

    /**
     * 获取量具配置
     */
    @ApiOperation("获取量具配置")
    @GetMapping("/getGaugeConfig")
    public R<?> getGaugeConfig(Long agentId) {
        List<GAUGE_CONNECTION_DTO> gaugeConfig = gaugeCommonService.getGaugeConfig(agentId);
        if (CollectionUtils.isNotEmpty(gaugeConfig)) {
            List<GAUGE_CONNECTION_CONFIG_DTO> collect = gaugeConfig.stream().map(GAUGE_CONNECTION_DTO::getGaugeConnectionConfigDto).collect(Collectors.toList());
            return R.ok(collect);
        } else {
            return R.fail();
        }
    }

    /**
     * 数据解析
     */
    @ApiOperation("串口调试解析接收数据")
    @PostMapping("/serialAnalysis")
    public R<?> serialAnalysis(@RequestBody SerialDebuggingVO serialDebuggingVO) {
        if (StringUtils.isEmpty(serialDebuggingVO.getContent()))
            return R.fail("未接收到数据");
        if (StringUtils.isEmpty(serialDebuggingVO.getHardwareId()))
            return R.fail("硬件信息未填写");
        if (StringUtils.isEmpty(serialDebuggingVO.getSerialPort()))
            return R.fail("串口信息未填写");
        return R.ok(gaugeCommonService.serialAnalysis(serialDebuggingVO));
    }

    /**
     * 获取redis连接信息返回
     */
    @ApiOperation("获取redis连接信息返回")
    @GetMapping("/getRedisConnectionInfo")
    public R<?> getRedisConnectionInfo() {
        HashMap<String, String> map = new HashMap<>();
        map.put("redisHost", redisHost);
        map.put("redisPort", redisPort);
        map.put("redisPassword", redisPassword);
        map.put("redisDatabase", redisDatabase);
        return R.ok(map);
    }

    /**
     * 手动更新公差限缓存
     */
    @ApiOperation("手动更新公差限缓存")
    @PostMapping("/updateParetoLimitCache")
    public R<?> updateParetoLimitCache() {
        /*初始化公差限(产品测试当key)  控制限(产品过程测试当key)*/
        redisService.delBatch(RedisConstant.SPEC_KEY+"*");
        SPEC_INF_VO specInfVo = new SPEC_INF_VO();
        specInfVo.setNext(Constants.NEXT);
        specInfVo.setF_DEL(YesOrNoEnum.NO.getType());
        List<SPEC_INF_DTO> specInfDtoList = specInfService.getList(specInfVo);
        for (SPEC_INF_DTO specInfDto : specInfDtoList) {
            String key = RedisConstant.SPEC_KEY +
                    specInfDto.getF_PART() + Constants.COMMA +
                    specInfDto.getF_PTRV() + Constants.COMMA +
                    specInfDto.getF_TEST();
            if(ObjectUtils.isNotEmpty(specInfDto.getF_PRCS())){
                key += Constants.COLON + specInfDto.getF_PRCS();
            }
            redisService.setCacheObject(key, specInfDto);
        }
        return R.ok();
    }
}
