package com.yingfei.dataManagement.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yingfei.dataManagement.YingFeiDataManageApplication;
import com.yingfei.dataManagement.service.CTRL_INFService;
import com.yingfei.dataManagement.service.SPEC_INFService;
import com.yingfei.entity.domain.BaseEntity;
import com.yingfei.entity.dto.CTRL_INF_DTO;
import com.yingfei.entity.vo.CTRL_INF_VO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
@SpringBootTest(classes = YingFeiDataManageApplication.class)
class SPEC_INFServiceImplTest {



//    @Resource
//    SPEC_INFService service;
//    @Test
//    void getSpecLim() {
////        final SPEC_INF_DTO specLim = service.getSpecLim(new SubgroupDataDTO(1950367292399525890L, 1950367292890259457L, 1907278308010299393L, null));
////        System.out.println(specLim);
////        System.out.println("\n");
////        final SPEC_INF_DTO specLim2 = service.getSpecLim(new SubgroupDataDTO(1950367292399525890L, 1950367292890259457L, 1907278308010299393L, 0L));
////        System.out.println(specLim2);
////        System.out.println("\n");
//        final SPEC_INF_DTO specLim3 = service.getSpecLim(new SubgroupDataDTO(1950367292399525890L, 1950367292890259457L, 1907278308010299393L, 1869280796592566274L));
//        System.out.println(specLim3);
//    }


//    @Resource
//    CTRL_INFService ctrlInfService;
//    @Test
//    void getInfo() {
//
//        final List<CTRL_INF_DTO> ctrlInfo = ctrlInfService.getCtrlInfo(new CTRL_INF_VO(1950367292399525890L, 1950367292890259457L, 1936409495001563137L, 1907278308010299393L));
//        System.out.println(JSONObject.toJSONString(ctrlInfo));
//    }

@Autowired
private ObjectMapper objectMapper;

    @Test
    public void testDateSerialization() throws JsonProcessingException {
        BaseEntity obj = new BaseEntity();
        obj.setF_CRTM(new Date());
        String json = objectMapper.writeValueAsString(obj);
        System.out.println(json); // 检查日期格式是否符合预期
    }
}
