@echo off
echo ================================
echo Jackson时间格式修复验证脚本
echo ================================
echo.

echo 1. 清理并重新编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码错误
    pause
    exit /b 1
)
echo 编译完成！

echo.
echo 2. 启动系统服务进行测试...
echo 请手动启动yingfei-system服务，然后测试以下接口：
echo.
echo 测试接口示例：
echo POST http://localhost:9210/oper_log_inf/list
echo Content-Type: application/json
echo.
echo 请求体：
echo {
echo   "offset": 0,
echo   "next": 10
echo }
echo.
echo 预期返回格式（时间字段应为字符串格式）：
echo {
echo   "code": 200,
echo   "data": {
echo     "rows": [
echo       {
echo         "F_CRTM": "2024-01-15T10:30:45.123Z",
echo         "F_EDTM": "2024-01-15T10:30:45.123Z"
echo       }
echo     ]
echo   }
echo }
echo.
echo 如果时间字段显示为数字（如1705312245123），说明修复未生效
echo 如果时间字段显示为字符串格式，说明修复成功
echo.
echo ================================
echo 修复内容总结：
echo ================================
echo 1. 移除了pom.xml中的jackson.version配置
echo 2. 完善了ApplicationConfig中的Jackson配置
echo 3. 为BaseEntity中的时间字段添加了@JsonFormat注解
echo 4. 统一使用UTC时区和ISO 8601格式
echo ================================
pause
