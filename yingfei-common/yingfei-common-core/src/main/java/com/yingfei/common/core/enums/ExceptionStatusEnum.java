//package com.yingfei.common.core.enums;
//
//import com.yingfei.common.core.exception.ServiceException;
//import com.yingfei.common.core.utils.I18nUtils;
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
//@Getter
//@AllArgsConstructor
//public enum ExceptionStatusEnum {
//
//    // 1-请假申请 2-外出申请 3-异常补卡处理申请 4-其他申请
//    ASK_FOR_LEAVE(1,"请假申请"),
//    GO_OUT(2,"外出申请"),
//    ABNORMAL_FILLING(3,"异常补卡处理申请"),
//    OTHER(4,"其他申请"),
//    ;
//
//
//    private final Integer type;
//    private final String description;
//
//    public static ExceptionStatusEnum find(Integer type) {
//        for (ExceptionStatusEnum value : ExceptionStatusEnum.values()) {
//            if (value.getType().equals(type)) {
//                return value;
//            }
//        }
//        throw new ServiceException(I18nUtils.getMessage("INVALID_TYPE"));
//    }
//}
