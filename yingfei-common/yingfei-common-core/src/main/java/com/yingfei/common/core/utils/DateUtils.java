package com.yingfei.common.core.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * 时间工具类
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static String YYYY_MM_DD_HH = "yyyy-MM-dd HH";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyy/M/d HH:mm:ss", "yyyy-MM-dd HH:mm:ss.s", "dd/MM/yyyy","yyyy/M/d HH:mm",};

    private static String[] parsePatterns_en = {
            "dd/MM/yy", "dd-MM-yyyy", "dd/MM/yyyy",
    };

    private static String pattern = "\\d{1,2}[-/]\\d{1,2}[-/]\\d{2,4}";

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, getNowDate());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String dateTimeOne(final Date date) {
        return parseDateToStr(YYYY, date);
    }

    public static final String dateTimeTwo(final Date date) {
        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, date);
    }

    public static final String dateTimeThree(final Date date) {
        return parseDateToStr(YYYY_MM, date);
    }

    public static final String dateTimeFour(final Date date) {
        return parseDateToStr(YYYY_MM_DD_HH_MM, date);
    }

    public static final String dateTimeFive(final Date date) {
        return parseDateToStr(YYYY_MM_DD_HH, date);
    }

    public static final String dateTimeSix(final Date date) {
        return parseDateToStr(YYYYMMDDHHMMSS, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = getNowDate();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = getNowDate();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }


    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            if (Pattern.matches(pattern, str.toString())) {
                return parseDate(str.toString(), parsePatterns_en);
            } else {
                return parseDate(str.toString(), parsePatterns);
            }
        } catch (ParseException e) {
            return null;
        }
    }

//    /**
//     * 获取服务器启动时间
//     */
//    public static Date getServerStartDate() {
//        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
//        return new Date(time);
//    }



    /**
     * 计算相差多少天 向上取整
     *
     * @return
     */
    public static Integer timeDistanceDay(Date endDate, Date startTime) {
        BigDecimal b = new BigDecimal(1000 * 24 * 60 * 60);
        BigDecimal decimal = new BigDecimal(endDate.getTime() - startTime.getTime());
        return decimal.divide(b, 0, RoundingMode.UP).intValue();
    }

//    /**
//     * 增加 LocalDateTime ==> Date
//     */
//    public static Date toDate(LocalDateTime temporalAccessor) {
//        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
//        return Date.from(zdt.toInstant());
//    }
//
//    /**
//     * 增加 LocalDate ==> Date
//     */
//    public static Date toDate(LocalDate temporalAccessor) {
//        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
//        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
//        return Date.from(zdt.toInstant());
//    }



    /**
     * 北京时间转UTC时间
     *
     * @param localDate
     * @return
     */
    public static Date toUtc(String localDate) {
        DateTimeFormatter formatter = null;
        LocalDateTime localDateTime = null;
        for (String format : parsePatterns) {
            try {
                formatter = DateTimeFormatter.ofPattern(format);
                localDateTime = LocalDateTime.parse(localDate, formatter);
                break;
            } catch (DateTimeParseException e) {
                // 忽略解析异常，尝试下一个格式
            }
        }
        if (localDateTime == null) {
            return toUtc(parseDate(localDate));
        }
        // 将 LocalDateTime 转换为 ZonedDateTime（北京时间）
        ZonedDateTime beijingDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        // 转换为 Instant（UTC 时间）
        Instant instant = beijingDateTime.toInstant();
        // 将 Instant 格式化为字符串
        String utcTime = instant.atZone(ZoneId.of("UTC")).format(formatter);
        return parseDate(utcTime);
    }

    public static Date toUtc(Date localDate) {
        // 将 Date 转换为 Instant
        Instant instant = localDate.toInstant();

        // 将 Instant 转换为 UTC 时间的 ZonedDateTime
        ZonedDateTime utcDateTime = instant.atZone(ZoneId.of("UTC"));

        // 格式化 ZonedDateTime 为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String utc = utcDateTime.format(formatter);
        return parseDate(utc);
    }

    public static void main(String[] args) {
        Date date = parseDate("2025/2/8 18:44:17");

        Date utc = toUtc(date);
        System.out.println("UTC Time: " + utc);
    }
}
