package com.yingfei.common.core.utils.poi;

import com.yingfei.common.core.annotation.Excel;
import com.yingfei.common.core.annotation.Excels;
import com.yingfei.common.core.constant.Constants;
import com.yingfei.common.core.text.Convert;
import com.yingfei.common.core.utils.DateUtils;
import com.yingfei.common.core.utils.I18nUtils;
import com.yingfei.common.core.utils.StringUtils;
import com.yingfei.common.core.utils.file.FileTypeUtils;
import com.yingfei.common.core.utils.file.ImageUtils;
import com.yingfei.common.core.utils.reflect.ReflectUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel相关处理
 */
public class ExcelUtil<T> {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    public static final String FORMULA_REGEX_STR = "=|-|\\+|@";

    public static final String[] FORMULA_STR = {"=", "-", "+", "@"};

    /**
     * Excel sheet最大行数，默认65536
     */
    public static final int sheetSize = 65536;

    /**
     * 工作表名称
     */
    private String sheetName;

    /**
     * 导出类型（EXPORT:导出数据；IMPORT：导入模板）
     */
    private Excel.Type type;

    /**
     * 工作薄对象
     */
    private Workbook wb;

    /**
     * 工作表对象
     */
    private Sheet sheet;

    /**
     * 样式列表
     */
    private Map<String, CellStyle> styles;

    /**
     * 导入导出数据列表
     */
    private List<T> list;

    /**
     * 注解列表
     */
    private List<Object[]> fields;

    /**
     * 当前行号
     */
    private int rownum;

    /**
     * 标题
     */
    private String title;

    /**
     * 最大高度
     */
    private short maxHeight;

    /**
     * 合并后最后行数
     */
    private int subMergedLastRowNum = 0;

    /**
     * 合并后开始行数
     */
    private int subMergedFirstRowNum = 1;

    /**
     * 对象的子列表方法
     */
    private Method subMethod;

    /**
     * 对象的子列表属性
     */
    private List<Field> subFields;

    /**
     * 统计列表
     */
    private Map<Integer, Double> statistics = new HashMap<Integer, Double>();

    /**
     * 数字格式
     */
    private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");

    /**
     * 实体对象
     */
    public Class<T> clazz;

    /**
     * 需要排除列属性
     */
    public String[] excludeFields;

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 隐藏Excel中列属性
     *
     * @param fields 列属性名 示例[单个"name"/多个"id","name"]
     * @throws Exception
     */
    public void hideColumn(String... fields) {
        this.excludeFields = fields;
    }

    public void init(List<T> list, String sheetName, String title, Excel.Type type) {
        if (list == null) {
            list = new ArrayList<T>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTitle();
        createSubHead();
    }

    /**
     * 创建excel第一行标题
     */
    public void createTitle() {
        if (StringUtils.isNotEmpty(title)) {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            int titleLastCol = this.fields.size() - 1;
            if (isSubList()) {
                titleLastCol = titleLastCol + subFields.size() - 1;
            }
            Row titleRow = sheet.createRow(rownum == 0 ? rownum++ : 0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(), titleLastCol));
        }
    }

    /**
     * 创建对象的子列表名称
     */
    public void createSubHead() {
        if (isSubList()) {
            subMergedFirstRowNum++;
            subMergedLastRowNum++;
            Row subRow = sheet.createRow(rownum);
            int excelNum = 0;
            for (Object[] objects : fields) {
                Excel attr = (Excel) objects[1];
                Cell headCell1 = subRow.createCell(excelNum);
                headCell1.setCellValue(attr.name());
                headCell1.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));
                excelNum++;
            }
            int headFirstRow = excelNum - 1;
            int headLastRow = headFirstRow + subFields.size() - 1;
            if (headLastRow > headFirstRow) {
                sheet.addMergedRegion(new CellRangeAddress(rownum, rownum, headFirstRow, headLastRow));
            }
            rownum++;
        }
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is 输入流
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is) throws Exception {
        return importExcel(is, 0);
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     *
     * @param is       输入流
     * @param titleNum 标题占用行数
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is, int titleNum) throws Exception {
        return importExcel(StringUtils.EMPTY, is, titleNum);
    }

    /**
     * 对excel表单指定表格索引名转换成list
     *
     * @param sheetName 表格索引名
     * @param titleNum  标题占用行数
     * @param is        输入流
     * @return 转换后集合
     */
    public List<T> importExcel(String sheetName, InputStream is, int titleNum) throws Exception {
        this.type = Excel.Type.IMPORT;
        this.wb = WorkbookFactory.create(is);
        List<T> list = new ArrayList<T>();
        // 如果指定sheet名,则取指定sheet中的内容 否则默认指向第1个sheet
        Sheet sheet = StringUtils.isNotEmpty(sheetName) ? wb.getSheet(sheetName) : wb.getSheetAt(0);
        if (sheet == null) {
            throw new IOException(I18nUtils.getMessage("FILE_SHEET_NOT_EXIST"));
        }

        // 获取最后一个非空行的行下标，比如总行数为n，则返回的为n-1
        int rows = sheet.getLastRowNum();

        if (rows > 0) {
            // 定义一个map用于存放excel列的序号和field.
            Map<String, Integer> cellMap = new HashMap<String, Integer>();
            // 获取表头
            Row heard = sheet.getRow(titleNum);
            for (int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
                Cell cell = heard.getCell(i);
                if (StringUtils.isNotNull(cell)) {
                    String value = this.getCellValue(heard, i).toString();
                    cellMap.put(value, i);
                } else {
                    cellMap.put(null, i);
                }
            }
            // 有数据时才处理 得到类的所有field.
            List<Object[]> fields = this.getFields();
            Map<Integer, Object[]> fieldsMap = new HashMap<Integer, Object[]>();
            for (Object[] objects : fields) {
                Excel attr = (Excel) objects[1];
                Integer column = cellMap.get(attr.name());
                if (column != null) {
                    fieldsMap.put(column, objects);
                }
            }
            for (int i = titleNum + 1; i <= rows; i++) {
                // 从第2行开始取数据,默认第一行是表头.
                Row row = sheet.getRow(i);
                // 判断当前行是否是空行
                if (isRowEmpty(row)) {
                    continue;
                }
                T entity = null;
                for (Map.Entry<Integer, Object[]> entry : fieldsMap.entrySet()) {
                    Object val = this.getCellValue(row, entry.getKey());
                    if (StringUtils.isNotEmpty(val.toString())) {
                        val = val.toString().trim();
                    }
                    // 如果不存在实例则新建.
                    entity = (entity == null ? clazz.newInstance() : entity);
                    // 从map中得到对应列的field.
                    Field field = (Field) entry.getValue()[0];
                    Excel attr = (Excel) entry.getValue()[1];
                    // 取得类型,并根据对象类型设置值.
                    Class<?> fieldType = field.getType();
                    if (String.class == fieldType) {
                        String s = Convert.toStr(val);
                        if (StringUtils.endsWith(s, ".0")) {
//                            val = StringUtils.substringBefore(s, ".0");
                        } else {
                            String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                            if (StringUtils.isNotEmpty(dateFormat)) {
                                val = parseDateToStr(dateFormat, val);
                            } else {
                                val = Convert.toStr(val);
                            }
                        }
                    } else if ((Integer.TYPE == fieldType || Integer.class == fieldType)) {
                        val = StringUtils.isNotEmpty(val.toString()) ? Convert.toInt(val) : Convert.toInt(attr.defaultValue());
                    } else if ((Long.TYPE == fieldType || Long.class == fieldType)) {
                        val = StringUtils.isNotEmpty(val.toString()) ? Convert.toLong(val) : Convert.toLong(attr.defaultValue());
                    } else if (Double.TYPE == fieldType || Double.class == fieldType) {
                        if (StringUtils.isNotEmpty(val.toString())) {
                            val = new BigDecimal(val.toString()).doubleValue();
                        } else {
                            val = Convert.toFloat(attr.defaultValue());
                        }
                    } else if (Float.TYPE == fieldType || Float.class == fieldType) {
                        val = StringUtils.isNotEmpty(val.toString()) ? Convert.toFloat(val) : Convert.toFloat(attr.defaultValue());
                    } else if (BigDecimal.class == fieldType) {
                        val = StringUtils.isNotEmpty(val.toString()) ? Convert.toBigDecimal(val) : Convert.toBigDecimal(attr.defaultValue());
                    } else if (Date.class == fieldType) {
                        if (val instanceof String) {
                            val = DateUtils.parseDate(val);
                        } else if (val instanceof Double) {
                            val = DateUtil.getJavaDate((Double) val);
                        }
                    } else if (Boolean.TYPE == fieldType || Boolean.class == fieldType) {
                        val = Convert.toBool(val, false);
                    }
                    if (StringUtils.isNotNull(fieldType)) {
                        String propertyName = field.getName();
                        if (StringUtils.isNotEmpty(attr.targetAttr())) {
                            propertyName = field.getName() + "." + attr.targetAttr();
                        } else if (StringUtils.isNotEmpty(attr.readConverterExp())) {
                            val = reverseByExp(Convert.toStr(val), attr.readConverterExp(), attr.separator());
                        } else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
                            val = dataFormatHandlerAdapter(val, attr);
                        }
                        ReflectUtils.invokeSetter(entity, propertyName, val);
                    }
                }
                list.add(entity);
            }
        }
        return list;
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param response  返回数据
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName) {
        exportExcel(response, list, sheetName, StringUtils.EMPTY);
    }

    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, List<MultipartFile> fileList) {
        exportFileExcel(response, list, sheetName, StringUtils.EMPTY, fileList);
    }

    /**
     * 导出excel 返回byte数组
     *
     * @param list      参数集合
     * @param sheetName 表格名字
     * @return
     */
    public byte[] exportUserExcel(List<T> list, String sheetName) {
        this.init(list, sheetName, title, Excel.Type.EXPORT);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            writeSheet(byteArrayOutputStream);
        } finally {
            IOUtils.closeQuietly(wb);
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 生成excel文件并且返回MultipartFile
     *
     * @param list      参数集合
     * @param sheetName 表名字
     * @param fileName  文件名字
     * @return
     */
    public MultipartFile createExcelFile(List<T> list, String sheetName, String fileName) {
        byte[] excelBytes = exportUserExcel(list, sheetName);
        return new MultipartFile() {
            @Override
            public String getName() {
                return fileName;
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            }

            @Override
            public boolean isEmpty() {
                return excelBytes.length == 0;
            }

            @Override
            public long getSize() {
                return excelBytes.length;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return excelBytes;
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new ByteArrayInputStream(excelBytes);
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                try (FileOutputStream fos = new FileOutputStream(dest)) {
                    fos.write(excelBytes);
                }
            }
        };
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param response  返回数据
     * @param list      导出数据集合
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Excel.Type.EXPORT);
        exportExcel(response);
    }

    public void exportFileExcel(HttpServletResponse response, List<T> list, String sheetName, String title, List<MultipartFile> fileList) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Excel.Type.EXPORT);
        exportExcel(response, fileList, null);
    }

    public void exportFileExcel(HttpServletResponse response, List<T> list, String sheetName, String title, List<MultipartFile> fileList, Map<Integer, List<ExcelFillIn>> map) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(list, sheetName, title, Excel.Type.EXPORT);
        exportExcel(response, fileList, map);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @return 结果
     */
    public void importTemplateExcel(HttpServletResponse response, String sheetName) {
        importTemplateExcel(response, sheetName, StringUtils.EMPTY);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     * @param title     标题
     * @return 结果
     */
    public void importTemplateExcel(HttpServletResponse response, String sheetName, String title) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.init(null, sheetName, title, Excel.Type.IMPORT);
        exportExcel(response);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @return 结果
     */
    public void exportExcel(HttpServletResponse response) {
        try {
            writeSheet(new ArrayList<>(), null);
            wb.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    public void exportExcel(HttpServletResponse response, List<MultipartFile> fileList, Map<Integer, List<ExcelFillIn>> map) {
        try {
            writeSheet(fileList, map);
            wb.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }


    /**
     * 将数据写入Excel工作表的方法
     *
     * @param outputStream 输出流，用于写入Excel数据
     */
    public void writeSheet(OutputStream outputStream) {
        // 取出一共有多少个sheet.
        int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / sheetSize));
        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);

            // 产生一行
            Row row = sheet.createRow(rownum);
            int column = 0;
            // 写入各个字段的列头名称
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, row, column++, fields.size());
                    }
                } else {
                    this.createHeadCell(excel, row, column++, fields.size());
                }
            }
            if (Excel.Type.EXPORT.equals(type)) {
                fillExcelData(index, row);
                addStatisticsRow();
            }
        }

        // 将工作簿内容写入提供的输出流
        try {
            wb.write(outputStream);
        } catch (IOException e) {
            log.error("写入Excel数据到输出流时出错：{}", e.getMessage());
        } finally {
            try {
                wb.close();
            } catch (IOException e) {
                log.error("关闭工作簿时出错：{}", e.getMessage());
            }
        }
    }

    // Helper method to get the value from a field in an object
    private Object getValueFromField(Object object, Field field) {
        field.setAccessible(true);
        try {
            return field.get(object);
        } catch (IllegalAccessException e) {
            log.error("Error accessing field value: {}", e.getMessage());
        }
        return null;
    }

    // Helper method to set the cell value based on the data type
    private void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
            CellStyle dateStyle = cell.getSheet().getWorkbook().createCellStyle();
            dateStyle.setDataFormat(cell.getSheet().getWorkbook().createDataFormat().getFormat("yyyy-MM-dd HH:mm:ss"));
            cell.setCellStyle(dateStyle);
        } else {
            cell.setCellValue(value.toString());
        }
    }


    /**
     * 创建写入数据到Sheet
     */
    public void writeSheet(List<MultipartFile> fileList, Map<Integer, List<ExcelFillIn>> map) {
        // 取出一共有多少个sheet.
        int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / sheetSize));

        for (int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);
//            Row row = sheet.createRow(rownum);
//            if (file != null) {
//                Cell cell = row.createCell(0);
//                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, fields.size()));
//                ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(),
//                        cell.getRow().getRowNum(), (short) (cell.getColumnIndex() + 1), cell.getRow().getRowNum() + 1);
//                byte[] data = ImageUtils.getImage(file);
//                getDrawingPatriarch(cell.getSheet()).createPicture(anchor,
//                        cell.getSheet().getWorkbook().addPicture(data, getImageType(data)));
//            }

            if (MapUtils.isNotEmpty(map)) {
                rownum = map.size() + 1;

                for (Integer key : map.keySet()) {
                    Row row = sheet.createRow(key);
                    Font boldFont = null;
                    if (key == 0) {
                        row.setHeightInPoints(16);
                        // 创建字体并设置为加粗
                        boldFont = wb.createFont();
                        boldFont.setBold(true); // 设置加粗
                    }
                    List<ExcelFillIn> excelFillIns = map.get(key);
                    for (ExcelFillIn excelFillIn : excelFillIns) {
                        // 创建列
                        Cell cell = row.createCell(excelFillIn.getColumnStart());
                        cell.setCellValue(excelFillIn.getContent());
                        // 合并单元格：从第1行第1列到第1行第3列
                        if (excelFillIn.merge) {
                            sheet.addMergedRegion(new CellRangeAddress(
                                    excelFillIn.getLineStart(), // 起始行索引
                                    excelFillIn.getLineEnd(), // 结束行索引
                                    excelFillIn.getColumnStart(), // 起始列索引
                                    excelFillIn.getColumnEnd()  // 结束列索引
                            ));
                            // 设置合并单元格的样式
                            CellStyle style = wb.createCellStyle();
                            style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                            style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
                            if (boldFont != null) {
                                style.setFont(boldFont);
                            }
                            cell.setCellStyle(style);

                        }
                    }
                }
            }

            // 产生一行
            Row row = sheet.createRow(rownum);

            int column = 0;
            // 写入各个字段的列头名称
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, row, column++, fields.size());
                    }
                } else {
                    this.createHeadCell(excel, row, column++, fields.size());
                }
            }
            if (Excel.Type.EXPORT.equals(type)) {
                fillExcelData(index, row, fileList);
                addStatisticsRow();
            }
        }
    }

    /**
     * 填充excel数据
     *
     * @param index 序号
     * @param row   单元格行
     */
    @SuppressWarnings("unchecked")
    public void fillExcelData(int index, Row row) {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        int rowNo = (1 + rownum) - startNo;
        for (int i = startNo; i < endNo; i++) {
            rowNo = isSubList() ? (i > 1 ? rowNo + 1 : rowNo + i) : i + 1 + rownum - startNo;
            row = sheet.createRow(rowNo);
            // 得到导出对象.
            T vo = (T) list.get(i);
            Collection<?> subList = null;
            if (isSubList()) {
                if (isSubListValue(vo)) {
                    subList = getListCellValue(vo);
                    subMergedLastRowNum = subMergedLastRowNum + subList.size();
                } else {
                    subMergedFirstRowNum++;
                    subMergedLastRowNum++;
                }
            }
            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType()) && StringUtils.isNotNull(subList)) {
                    boolean subFirst = false;
                    for (Object obj : subList) {
                        if (subFirst) {
                            rowNo++;
                            row = sheet.createRow(rowNo);
                        }
                        List<Field> subFields = FieldUtils.getFieldsListWithAnnotation(obj.getClass(), Excel.class);
                        int subIndex = 0;
                        for (Field subField : subFields) {
                            if (subField.isAnnotationPresent(Excel.class)) {
                                subField.setAccessible(true);
                                Excel attr = subField.getAnnotation(Excel.class);
                                this.addCell(attr, row, (T) obj, subField, column + subIndex);
                            }
                            subIndex++;
                        }
                        subFirst = true;
                    }
                    this.subMergedFirstRowNum = this.subMergedFirstRowNum + subList.size();
                } else {
                    this.addCell(excel, row, vo, field, column++);
                }
            }
        }
    }


    /**
     * 填充excel数据
     *
     * @param index 序号
     * @param row   单元格行
     */
    @SuppressWarnings("unchecked")
    public void fillExcelData(int index, Row row, List<MultipartFile> fileList) {
        int startNo = index * sheetSize;
        int endNo = Math.min(startNo + sheetSize, list.size());
        int rowNo = (1 + rownum) - startNo;
        for (int i = startNo; i < endNo; i++) {
            rowNo = isSubList() ? (i > 1 ? rowNo + 1 : rowNo + i) : i + 1 + rownum - startNo;
            row = sheet.createRow(rowNo);
            // 得到导出对象.
            T vo = (T) list.get(i);
            Collection<?> subList = null;
            if (isSubList()) {
                if (isSubListValue(vo)) {
                    subList = getListCellValue(vo);
                    subMergedLastRowNum = subMergedLastRowNum + subList.size();
                } else {
                    subMergedFirstRowNum++;
                    subMergedLastRowNum++;
                }
            }
            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType()) && StringUtils.isNotNull(subList)) {
                    boolean subFirst = false;
                    for (Object obj : subList) {
                        if (subFirst) {
                            rowNo++;
                            row = sheet.createRow(rowNo);
                        }
                        List<Field> subFields = FieldUtils.getFieldsListWithAnnotation(obj.getClass(), Excel.class);
                        int subIndex = 0;
                        for (Field subField : subFields) {
                            if (subField.isAnnotationPresent(Excel.class)) {
                                subField.setAccessible(true);
                                Excel attr = subField.getAnnotation(Excel.class);
                                this.addCell(attr, row, (T) obj, subField, column + subIndex);
                            }
                            subIndex++;
                        }
                        subFirst = true;
                    }
                    this.subMergedFirstRowNum = this.subMergedFirstRowNum + subList.size();
                } else {
                    this.addCell(excel, row, vo, field, column++);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(fileList)) {
            // 获取当前sheet的最后一行号，确保图片不会覆盖数据
            int lastRowNum = sheet.getLastRowNum();
            int imageStartRow = lastRowNum + 2; // 在数据后留一行空白，然后插入图片

            int num = 0;
            for (MultipartFile file : fileList) {
                int imageRowIndex = imageStartRow + num;
                row = sheet.createRow(imageRowIndex);
                /*设置行的高度*/
                row.setHeightInPoints(117f);
                Cell cell = row.createCell(0); // 使用第0列而不是行号作为列索引
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                byte[] data = ImageUtils.getImage(file);
                try (InputStream inputStream = file.getInputStream()) {
                    BufferedImage bufferImg = ImageIO.read(inputStream);
                    //原始高度
                    int width = bufferImg.getWidth();
                    int height = bufferImg.getHeight();
                    // 插入图片
                    /**
                     * dx1：图片左上角相对于起始单元格的 x 坐标偏移量，单位为 EMU（English Metric Units）。
                     * dy1：图片左上角相对于起始单元格的 y 坐标偏移量，单位为 EMU。
                     * dx2：图片右下角相对于终止单元格的 x 坐标偏移量，单位为 EMU。
                     * dy2：图片右下角相对于终止单元格的 y 坐标偏移量，单位为 EMU。
                     * col1：图片起始列。
                     * row1：图片起始行。
                     * col2：图片终止列。
                     * row2：图片终止行
                     */
                    XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) 0,
                            imageRowIndex, (short) 3, imageRowIndex + 1);
                    Picture picture = drawingPatriarch.createPicture(anchor, wb
                            .addPicture(data, XSSFWorkbook.PICTURE_TYPE_PNG));
                    // 调整图片大小
                    picture.resize();
                    num++;
                } catch (Exception e) {
                    log.error("插入图片失败: {}", e.getMessage(), e);
                }

            }
        }

    }

    /**
     * 创建表格样式
     *
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        styles.putAll(annotationHeaderStyles(wb, styles));

        styles.putAll(annotationDataStyles(wb));

        return styles;
    }

    /**
     * 根据Excel注解创建表格头样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationHeaderStyles(Workbook wb, Map<String, CellStyle> styles) {
        Map<String, CellStyle> headerStyles = new HashMap<String, CellStyle>();
        if (fields == null) {
            return headerStyles;
        }
        for (Object[] os : fields) {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("header_{}_{}", excel.headerColor(), excel.headerBackgroundColor());
            if (!headerStyles.containsKey(key)) {
                CellStyle style = wb.createCellStyle();
                style.cloneStyleFrom(styles.get("data"));
                style.setAlignment(HorizontalAlignment.CENTER);
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setFillForegroundColor(excel.headerBackgroundColor().index);
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                Font headerFont = wb.createFont();
                headerFont.setFontName("Arial");
                headerFont.setFontHeightInPoints((short) 10);
                headerFont.setBold(true);
                headerFont.setColor(excel.headerColor().index);
                style.setFont(headerFont);
                headerStyles.put(key, style);
            }
        }
        return headerStyles;
    }

    /**
     * 根据Excel注解创建表格列样式
     *
     * @param wb 工作薄对象
     * @return 自定义样式列表
     */
    private Map<String, CellStyle> annotationDataStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        if (fields == null) {
            return styles;
        }
        for (Object[] os : fields) {
            Excel excel = (Excel) os[1];
            String key = StringUtils.format("data_{}_{}_{}", excel.align(), excel.color(), excel.backgroundColor());
            if (!styles.containsKey(key)) {
                CellStyle style = wb.createCellStyle();
                style.setAlignment(excel.align());
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setBorderRight(BorderStyle.THIN);
                style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderLeft(BorderStyle.THIN);
                style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderTop(BorderStyle.THIN);
                style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setBorderBottom(BorderStyle.THIN);
                style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                style.setFillForegroundColor(excel.backgroundColor().getIndex());
                Font dataFont = wb.createFont();
                dataFont.setFontName("Arial");
                dataFont.setFontHeightInPoints((short) 10);
                dataFont.setColor(excel.color().index);
                style.setFont(dataFont);
                styles.put(key, style);
            }
        }
        return styles;
    }

    /**
     * 创建单元格
     */
    public Cell createHeadCell(Excel attr, Row row, int column, int size) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(attr.name());
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get(StringUtils.format("header_{}_{}", attr.headerColor(), attr.headerBackgroundColor())));

        if (attr.textType() == 1) {
            // 创建 CellStyle 对象并设置格式为文本
            CellStyle textStyle = wb.createCellStyle();
            DataFormat format = wb.createDataFormat();
            textStyle.setDataFormat(format.getFormat("@"));

            // 设置列的默认样式为文本格式，而不是预创建行
            // 这样可以避免覆盖数据行
            sheet.setDefaultColumnStyle(column, textStyle);
        }


        if (isSubList()) {
            // 填充默认样式，防止合并单元格样式失效
            sheet.setDefaultColumnStyle(column, styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));
            if (attr.needMerge()) {
                sheet.addMergedRegion(new CellRangeAddress(rownum - 1, rownum, column, column));
            }
        }
        return cell;
    }

    /**
     * 设置单元格信息
     *
     * @param value 单元格值
     * @param attr  注解相关
     * @param cell  单元格信息
     */
    public void setCellVo(Object value, Excel attr, Cell cell) {
        if (Excel.ColumnType.STRING == attr.cellType()) {
            String cellValue = Convert.toStr(value);
            // 对于任何以表达式触发字符 =-+@开头的单元格，直接使用tab字符作为前缀，防止CSV注入。
            if (StringUtils.startsWithAny(cellValue, FORMULA_STR)) {
                cellValue = RegExUtils.replaceFirst(cellValue, FORMULA_REGEX_STR, "\t$0");
            }
            if (value instanceof Collection && StringUtils.equals("[]", cellValue)) {
                cellValue = StringUtils.EMPTY;
            }
            cell.setCellValue(StringUtils.isNull(cellValue) ? attr.defaultValue() : cellValue + attr.suffix());
        } else if (Excel.ColumnType.NUMERIC == attr.cellType()) {
            if (StringUtils.isNotNull(value)) {
                cell.setCellValue(StringUtils.contains(Convert.toStr(value), ".") ? Convert.toDouble(value) : Convert.toInt(value));
            }
        } else if (Excel.ColumnType.IMAGE == attr.cellType()) {
            ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRow().getRowNum(), (short) (cell.getColumnIndex() + 1), cell.getRow().getRowNum() + 1);
            String imagePath = Convert.toStr(value);
            if (StringUtils.isNotEmpty(imagePath)) {
                byte[] data = ImageUtils.getImage(imagePath);
                getDrawingPatriarch(cell.getSheet()).createPicture(anchor,
                        cell.getSheet().getWorkbook().addPicture(data, getImageType(data)));
            }
        }
    }

    /**
     * 获取画布
     */
    public static Drawing<?> getDrawingPatriarch(Sheet sheet) {
        if (sheet.getDrawingPatriarch() == null) {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    /**
     * 获取图片类型,设置图片插入类型
     */
    public int getImageType(byte[] value) {
        String type = FileTypeUtils.getFileExtendName(value);
        if ("JPG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_JPEG;
        } else if ("PNG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    /**
     * 创建表格样式
     */
    public void setDataValidation(Excel attr, Row row, int column) {
        if (attr.name().indexOf("注：") >= 0) {
            sheet.setColumnWidth(column, 6000);
        } else {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
        }
        if (StringUtils.isNotEmpty(attr.prompt()) || attr.combo().length > 0) {
            if (attr.combo().length > 15 || StringUtils.join(attr.combo()).length() > 255) {
                // 如果下拉数大于15或字符串长度大于255，则使用一个新sheet存储，避免生成的模板下拉值获取不到
                setXSSFValidationWithHidden(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            } else {
                // 提示信息或只能选择不能输入的列内容.
                setPromptOrValidation(sheet, attr.combo(), attr.prompt(), 1, 100, column, column);
            }
        }
    }

    /**
     * 添加单元格
     */
    public Cell addCell(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell = null;
        try {
            // 设置行高
            row.setHeight(maxHeight);
            // 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
            if (attr.isExport()) {
                // 创建cell
                cell = row.createCell(column);
                if (isSubListValue(vo) && getListCellValue(vo).size() > 1 && attr.needMerge()) {
                    CellRangeAddress cellAddress = new CellRangeAddress(subMergedFirstRowNum, subMergedLastRowNum, column, column);
                    sheet.addMergedRegion(cellAddress);
                }
                cell.setCellStyle(styles.get(StringUtils.format("data_{}_{}_{}", attr.align(), attr.color(), attr.backgroundColor())));

                // 用于读取对象中的属性
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                String separator = attr.separator();
                if (StringUtils.isNotEmpty(dateFormat) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(parseDateToStr(dateFormat, value));
                } else if (StringUtils.isNotEmpty(readConverterExp) && StringUtils.isNotNull(value)) {
                    cell.setCellValue(convertByExp(Convert.toStr(value), readConverterExp, separator));
                } else if (value instanceof BigDecimal && -1 != attr.scale()) {
                    cell.setCellValue((((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).doubleValue());
                } else if (!attr.handler().equals(ExcelHandlerAdapter.class)) {
                    cell.setCellValue(dataFormatHandlerAdapter(value, attr));
                } else {
                    // 设置列类型
                    setCellVo(value, attr, cell);
                }
                addStatisticsData(column, Convert.toStr(value), attr);
            }
        } catch (Exception e) {
            log.error("导出Excel失败{}", e);
        }
        return cell;
    }

    /**
     * 设置 POI XSSFSheet 单元格提示或选择框
     *
     * @param sheet         表单
     * @param textlist      下拉框显示的内容
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setPromptOrValidation(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow,
                                      int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = textlist.length > 0 ? helper.createExplicitListConstraint(textlist) : helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent)) {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        sheet.addValidationData(dataValidation);
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框（兼容超出一定数量的下拉框）.
     *
     * @param sheet         要设置的sheet.
     * @param textlist      下拉框显示的内容
     * @param promptContent 提示内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     */
    public void setXSSFValidationWithHidden(Sheet sheet, String[] textlist, String promptContent, int firstRow, int endRow, int firstCol, int endCol) {
        String hideSheetName = "combo_" + firstCol + "_" + endCol;
        Sheet hideSheet = wb.createSheet(hideSheetName); // 用于存储 下拉菜单数据
        for (int i = 0; i < textlist.length; i++) {
            hideSheet.createRow(i).createCell(0).setCellValue(textlist[i]);
        }
        // 创建名称，可被其他单元格引用
        Name name = wb.createName();
        name.setNameName(hideSheetName + "_data");
        name.setRefersToFormula(hideSheetName + "!$A$1:$A$" + textlist.length);
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hideSheetName + "_data");
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        if (StringUtils.isNotEmpty(promptContent)) {
            // 如果设置了提示信息则鼠标放上去提示
            dataValidation.createPromptBox("", promptContent);
            dataValidation.setShowPromptBox(true);
        }
        // 处理Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
        // 设置hiddenSheet隐藏
        wb.setSheetHidden(wb.getSheetIndex(hideSheet), true);
    }

    /**
     * 解析导出值 0=男,1=女,2=未知
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String convertByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(Constants.COMMA);
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(propertyValue, separator)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[0].equals(value)) {
                        propertyString.append(itemArray[1] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 反向解析值 男=0,女=1,未知=2
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @param separator     分隔符
     * @return 解析后值
     */
    public static String reverseByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(Constants.COMMA);
        for (String item : convertSource) {
            String[] itemArray = item.split("=");
            if (StringUtils.containsAny(propertyValue, separator)) {
                for (String value : propertyValue.split(separator)) {
                    if (itemArray[1].equals(value)) {
                        propertyString.append(itemArray[0] + separator);
                        break;
                    }
                }
            } else {
                if (itemArray[1].equals(propertyValue)) {
                    return itemArray[0];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 数据处理器
     *
     * @param value 数据值
     * @param excel 数据注解
     * @return
     */
    public String dataFormatHandlerAdapter(Object value, Excel excel) {
        try {
            Object instance = excel.handler().newInstance();
            Method formatMethod = excel.handler().getMethod("format", new Class[]{Object.class, String[].class});
            value = formatMethod.invoke(instance, value, excel.args());
        } catch (Exception e) {
            log.error("不能格式化数据 " + excel.handler(), e.getMessage());
        }
        return Convert.toStr(value);
    }

    /**
     * 合计统计信息
     */
    private void addStatisticsData(Integer index, String text, Excel entity) {
        if (entity != null && entity.isStatistics()) {
            Double temp = 0D;
            if (!statistics.containsKey(index)) {
                statistics.put(index, temp);
            }
            try {
                temp = Double.valueOf(text);
            } catch (NumberFormatException e) {
            }
            statistics.put(index, statistics.get(index) + temp);
        }
    }

    /**
     * 创建统计行
     */
    public void addStatisticsRow() {
        if (statistics.size() > 0) {
            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            Set<Integer> keys = statistics.keySet();
            Cell cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for (Integer key : keys) {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                cell.setCellValue(DOUBLE_FORMAT.format(statistics.get(key)));
            }
            statistics.clear();
        }
    }

    /**
     * 获取bean中的属性值
     *
     * @param vo    实体对象
     * @param field 字段
     * @param excel 注解
     * @return 最终的属性值
     * @throws Exception
     */
    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
        Object o = field.get(vo);
        if (StringUtils.isNotEmpty(excel.targetAttr())) {
            String target = excel.targetAttr();
            if (target.contains(".")) {
                String[] targets = target.split("[.]");
                for (String name : targets) {
                    o = getValue(o, name);
                }
            } else {
                o = getValue(o, target);
            }
        }
        return o;
    }

    /**
     * 以类的属性的get方法方法形式获取值
     *
     * @param o
     * @param name
     * @return value
     * @throws Exception
     */
    private Object getValue(Object o, String name) throws Exception {
        if (StringUtils.isNotNull(o) && StringUtils.isNotEmpty(name)) {
            Class<?> clazz = o.getClass();
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            o = field.get(o);
        }
        return o;
    }

    /**
     * 得到所有定义字段
     */
    private void createExcelField() {
        this.fields = getFields();
        this.fields = this.fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        this.maxHeight = getRowHeight();
    }

    /**
     * 获取字段注解信息
     */
    public List<Object[]> getFields() {
        List<Object[]> fields = new ArrayList<Object[]>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for (Field field : tempFields) {
            if (!ArrayUtils.contains(this.excludeFields, field.getName())) {
                // 单注解
                if (field.isAnnotationPresent(Excel.class)) {
                    Excel attr = field.getAnnotation(Excel.class);
                    if (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type)) {
                        field.setAccessible(true);
                        fields.add(new Object[]{field, attr});
                    }
                    if (Collection.class.isAssignableFrom(field.getType())) {
                        subMethod = getSubMethod(field.getName(), clazz);
                        ParameterizedType pt = (ParameterizedType) field.getGenericType();
                        Class<?> subClass = (Class<?>) pt.getActualTypeArguments()[0];
                        this.subFields = FieldUtils.getFieldsListWithAnnotation(subClass, Excel.class);
                    }
                }

                // 多注解
                if (field.isAnnotationPresent(Excels.class)) {
                    Excels attrs = field.getAnnotation(Excels.class);
                    Excel[] excels = attrs.value();
                    for (Excel attr : excels) {
                        if (!ArrayUtils.contains(this.excludeFields, field.getName() + "." + attr.targetAttr())
                                && (attr != null && (attr.type() == Excel.Type.ALL || attr.type() == type))) {
                            field.setAccessible(true);
                            fields.add(new Object[]{field, attr});
                        }
                    }
                }
            }
        }
        return fields;
    }

    /**
     * 根据注解获取最大行高
     */
    public short getRowHeight() {
        double maxHeight = 0;
        for (Object[] os : this.fields) {
            Excel excel = (Excel) os[1];
            maxHeight = Math.max(maxHeight, excel.height());
        }
        return (short) (maxHeight * 20);
    }

    /**
     * 创建一个工作簿
     */
    public void createWorkbook() {
        this.wb = new SXSSFWorkbook(500);
        this.sheet = wb.createSheet();
        wb.setSheetName(0, sheetName);
        this.styles = createStyles(wb);
    }

    /**
     * 创建工作表
     *
     * @param sheetNo sheet数量
     * @param index   序号
     */
    public void createSheet(int sheetNo, int index) {
        // 设置工作表的名称.
        if (sheetNo > 1 && index > 0) {
            this.sheet = wb.createSheet();
            this.createTitle();
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 获取单元格值
     *
     * @param row    获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    public Object getCellValue(Row row, int column) {
        if (row == null) {
            return row;
        }
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if (StringUtils.isNotNull(cell)) {
                if (cell.getCellType() == CellType.NUMERIC || cell.getCellType() == CellType.FORMULA) {
                    val = cell.getNumericCellValue();
                    if (DateUtil.isCellDateFormatted(cell)) {
                        val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
                    } else {
                        if ((Double) val % 1 != 0) {
                            val = new BigDecimal(val.toString());
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if (cell.getCellType() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if (cell.getCellType() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }

            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }

    /**
     * 判断是否是空行
     *
     * @param row 判断的行
     * @return
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    /**
     * 格式化不同类型的日期对象
     *
     * @param dateFormat 日期格式
     * @param val        被格式化的日期对象
     * @return 格式化后的日期字符
     */
    public String parseDateToStr(String dateFormat, Object val) {
        if (val == null) {
            return "";
        }
        String str;
        if (val instanceof Date) {
            str =  cn.hutool.core.date.DateUtil.format((Date) val, dateFormat);
        } else if (val instanceof LocalDateTime) {
            final LocalDateTime val1 = (LocalDateTime) val;
            str = val1.format(DateTimeFormatter.ofPattern(dateFormat));
        } else if (val instanceof LocalDate) {
            final LocalDate val1 = (LocalDate) val;
            str = val1.format(DateTimeFormatter.ofPattern(dateFormat));
        } else {
            str = val.toString();
        }
        return str;
    }

    /**
     * 是否有对象的子列表
     */
    public boolean isSubList() {
        return StringUtils.isNotNull(subFields) && subFields.size() > 0;
    }

    /**
     * 是否有对象的子列表，集合不为空
     */
    public boolean isSubListValue(T vo) {
        return StringUtils.isNotNull(subFields) && subFields.size() > 0 && StringUtils.isNotNull(getListCellValue(vo)) && getListCellValue(vo).size() > 0;
    }

    /**
     * 获取集合的值
     */
    public Collection<?> getListCellValue(Object obj) {
        Object value;
        try {
            value = subMethod.invoke(obj, new Object[]{});
        } catch (Exception e) {
            return new ArrayList<Object>();
        }
        return (Collection<?>) value;
    }

    /**
     * 获取对象的子列表方法
     *
     * @param name      名称
     * @param pojoClass 类对象
     * @return 子列表方法
     */
    public Method getSubMethod(String name, Class<?> pojoClass) {
        StringBuffer getMethodName = new StringBuffer("get");
        getMethodName.append(name.substring(0, 1).toUpperCase());
        getMethodName.append(name.substring(1));
        Method method = null;
        try {
            method = pojoClass.getMethod(getMethodName.toString(), new Class[]{});
        } catch (Exception e) {
            log.error("获取对象异常{}", e.getMessage());
        }
        return method;
    }
    /**
     * 在同一个工作表中导出多个具有相同表头的数据表，支持 ExcelFillIn 数据填充和图片导入
     *
     * @param response    返回数据
     * @param lists       多个导出数据集合，每个 List<T> 代表一个数据表
     * @param tableNames  多个数据表的名称
     * @param sheetName   工作表名称
     * @param title       标题
     * @param fileList    图片文件列表
     * @param fillInMap     Excel 填充信息列表
     */
    public void exportMultipleSameHeaderInOneSheet(HttpServletResponse response, List<List<T>> lists, List<String> tableNames, String sheetName, String title, List<MultipartFile> fileList,Map<Integer, List<ExcelFillIn>> fillInMap) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        this.wb = new SXSSFWorkbook(500);
        this.styles = createStyles(wb);
        this.styles.putAll(annotationHeaderStyles(wb, this.styles));
        this.type = Excel.Type.EXPORT;
        this.title = title;
        createExcelField();

        int currentSheetIndex = 0;
        this.sheet = wb.createSheet(sheetName + currentSheetIndex);
        int currentRow = 0;

        if (MapUtils.isNotEmpty(fillInMap)) {
            currentRow = fillInMap.size() + 1;

            for (Integer key : fillInMap.keySet()) {
                Row row = sheet.createRow(key);
                Font boldFont = null;
                if (key == 0) {
                    row.setHeightInPoints(16);
                    // 创建字体并设置为加粗
                    boldFont = wb.createFont();
                    boldFont.setBold(true); // 设置加粗
                }
                List<ExcelFillIn> excelFillIns = fillInMap.get(key);
                for (ExcelFillIn excelFillIn : excelFillIns) {
                    // 创建列
                    Cell cell = row.createCell(excelFillIn.getColumnStart());
                    cell.setCellValue(excelFillIn.getContent());
                    // 合并单元格：从第1行第1列到第1行第3列
                    if (excelFillIn.merge) {
                        sheet.addMergedRegion(new CellRangeAddress(
                                excelFillIn.getLineStart(), // 起始行索引
                                excelFillIn.getLineEnd(), // 结束行索引
                                excelFillIn.getColumnStart(), // 起始列索引
                                excelFillIn.getColumnEnd()  // 结束列索引
                        ));
                        // 设置合并单元格的样式
                        CellStyle style = wb.createCellStyle();
                        style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                        style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
                        if (boldFont != null) {
                            style.setFont(boldFont);
                        }
                        cell.setCellStyle(style);

                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(title)) {
            if (currentRow >= sheetSize) {
                currentSheetIndex++;
                this.sheet = wb.createSheet(sheetName + currentSheetIndex);
                currentRow = 0;
            }
            Row titleRow = sheet.createRow(currentRow++);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            int titleLastCol = this.fields.size() - 1;
            if (isSubList()) {
                titleLastCol = titleLastCol + subFields.size() - 1;
            }
            CellRangeAddress titleRange = new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), 0, titleLastCol);
            if (!isMergedRegionOverlap(sheet, titleRange)) {
                sheet.addMergedRegion(titleRange);
            }
            currentRow++; // 为标题下方留空行
        }

        for (int i = 0; i < lists.size(); i++) {
            List<T> list = lists.get(i);
            String tableName = tableNames.get(i);

            // 预估当前数据表所需行数，包含表名、表头、数据行和统计行
            int requiredRows = 1 + 1 + list.size() + 1;
            if (currentRow + requiredRows >= sheetSize) {
                currentSheetIndex++;
                this.sheet = wb.createSheet(sheetName + currentSheetIndex);
                currentRow = 0;
            }

            // 写入数据表名称
            Row tableNameRow = sheet.createRow(currentRow++);
            Cell tableNameCell = tableNameRow.createCell(0);
            tableNameCell.setCellValue(tableName);
            CellStyle tableNameStyle = wb.createCellStyle();
            Font tableNameFont = wb.createFont();
            tableNameFont.setBold(true);
            tableNameFont.setFontHeightInPoints((short) 12);
            tableNameStyle.setFont(tableNameFont);
            int lastCol = this.fields.size() - 1;
            if (isSubList()) {
                lastCol = lastCol + subFields.size() - 1;
            }
            CellRangeAddress tableNameRange = new CellRangeAddress(tableNameRow.getRowNum(), tableNameRow.getRowNum(), 0, lastCol);
            if (!isMergedRegionOverlap(sheet, tableNameRange)) {
                sheet.addMergedRegion(tableNameRange);
            }
            tableNameCell.setCellStyle(tableNameStyle);

            // 写入表头
            Row headerRow = sheet.createRow(currentRow++);
            int column = 0;
            for (Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                if (Collection.class.isAssignableFrom(field.getType())) {
                    for (Field subField : subFields) {
                        Excel subExcel = subField.getAnnotation(Excel.class);
                        this.createHeadCell(subExcel, headerRow, column++, fields.size());
                    }
                } else {
                    this.createHeadCell(excel, headerRow, column++, fields.size());
                }
            }

            // 写入数据
            int startRow = currentRow;
            for (T item : list) {
                if (currentRow >= sheetSize) {
                    currentSheetIndex++;
                    this.sheet = wb.createSheet(sheetName + currentSheetIndex);
                    currentRow = 0;
                    // 重新写入表名和表头
                    tableNameRow = sheet.createRow(currentRow++);
                    tableNameCell = tableNameRow.createCell(0);
                    tableNameCell.setCellValue(tableName);
                    tableNameCell.setCellStyle(tableNameStyle);
                    CellRangeAddress newTableNameRange = new CellRangeAddress(tableNameRow.getRowNum(), tableNameRow.getRowNum(), 0, lastCol);
                    if (!isMergedRegionOverlap(sheet, newTableNameRange)) {
                        sheet.addMergedRegion(newTableNameRange);
                    }

                    headerRow = sheet.createRow(currentRow++);
                    column = 0;
                    for (Object[] os : fields) {
                        Field field = (Field) os[0];
                        Excel excel = (Excel) os[1];
                        if (Collection.class.isAssignableFrom(field.getType())) {
                            for (Field subField : subFields) {
                                Excel subExcel = subField.getAnnotation(Excel.class);
                                this.createHeadCell(subExcel, headerRow, column++, fields.size());
                            }
                        } else {
                            this.createHeadCell(excel, headerRow, column++, fields.size());
                        }
                    }
                    startRow = currentRow;
                }
                Row dataRow = sheet.createRow(currentRow++);
                column = 0;
                for (Object[] os : fields) {
                    Field field = (Field) os[0];
                    Excel excel = (Excel) os[1];
                    this.addCell(excel, dataRow, item, field, column++);
                }
            }

            // 添加统计行
            if (currentRow >= sheetSize) {
                currentSheetIndex++;
                this.sheet = wb.createSheet(sheetName + currentSheetIndex);
                currentRow = 0;
            }
            addStatisticsRowInRange(startRow, currentRow - 1);
            currentRow++; // 为下一个数据表留空行
        }

        // 处理图片导入
        if (CollectionUtils.isNotEmpty(fileList)) {
            int num = 0;
            for (MultipartFile file : fileList) {
                if (currentRow >= sheetSize) {
                    currentSheetIndex++;
                    this.sheet = wb.createSheet(sheetName + currentSheetIndex);
                    currentRow = 0;
                }
                Row imageRow = sheet.createRow(currentRow++);
                imageRow.setHeightInPoints(117f);
                Cell imageCell = imageRow.createCell(0);
                Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
                try {
                    byte[] data = ImageUtils.getImage(file);
                    try (InputStream inputStream = file.getInputStream()) {
                        BufferedImage bufferImg = ImageIO.read(inputStream);
                        int width = bufferImg.getWidth();
                        int height = bufferImg.getHeight();
                        XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) 0,
                                imageCell.getRow().getRowNum(), (short) 3, imageCell.getRow().getRowNum() + 1);
                        Picture picture = drawingPatriarch.createPicture(anchor, wb
                                .addPicture(data, XSSFWorkbook.PICTURE_TYPE_PNG));
                        picture.resize();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("插入图片失败: {}", e.getMessage());
                }
            }
        }

        try {
            wb.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("在同一个工作表中导出多个数据表异常: {}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }
    /**
     * 在指定行范围内添加统计行
     *
     * @param startRow 起始行
     * @param endRow   结束行
     */
    private void addStatisticsRowInRange(int startRow, int endRow) {
        if (statistics.size() > 0) {
            Row row = sheet.createRow(endRow + 1);
            Set<Integer> keys = statistics.keySet();
            Cell cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for (Integer key : keys) {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                double sum = 0;
                for (int i = startRow; i <= endRow; i++) {
                    Row dataRow = sheet.getRow(i);
                    if (dataRow != null) {
                        Cell dataCell = dataRow.getCell(key);
                        if (dataCell != null && dataCell.getCellType() == CellType.NUMERIC) {
                            sum += dataCell.getNumericCellValue();
                        }
                    }
                }
                cell.setCellValue(DOUBLE_FORMAT.format(sum));
            }
            statistics.clear();
        }
    }
    /**
     * 检查新的合并区域是否与现有的合并区域重叠
     * @param sheet 工作表对象
     * @param newRange 新的合并区域
     * @return 如果重叠返回 true，否则返回 false
     */
    private boolean isMergedRegionOverlap(Sheet sheet, CellRangeAddress newRange) {
        int numMergedRegions = sheet.getNumMergedRegions();
        for (int i = 0; i < numMergedRegions; i++) {
            CellRangeAddress existingRange = sheet.getMergedRegion(i);
            if (existingRange.intersects(newRange)) {
                return true;
            }
        }
        return false;
    }
}
