package com.yingfei.common.core.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity基类
 *
 *
 */
@Getter
@Setter
public class DataBaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 搜索值 */
    @JsonIgnore
    @ApiModelProperty("搜索值")
    private String searchValue;


    /** 创建者 */
    @ApiModelProperty("创建者")
    private String createBy;
    /** 创建时间 */
    @ApiModelProperty("创建时间")

    private Date createTime;

    /** 更新者 */
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新时间 */
    @ApiModelProperty("更新时间")

    private Date updateTime;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 请求参数 */
    @ApiModelProperty("请求参数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

    public Map<String, Object> getParams()
    {
        if (params == null)
        {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params)
    {
        this.params = params;
    }
}
