# Jackson时间格式修复说明

## 问题描述
接口返回值中的时间类型字段返回数字时间戳，而不是预期的ISO 8601格式字符串。

## 问题原因分析

### 1. Jackson版本冲突
- **问题**：pom.xml中指定了过时的Jackson版本 `2.9.10`
- **影响**：与Spring Boot 2.7.7的默认Jackson版本不兼容
- **解决**：移除自定义Jackson版本，让Spring Boot管理

### 2. 配置不完整
- **问题**：ApplicationConfig中的Jackson配置不完整
- **影响**：缺少日期格式和序列化特性配置
- **解决**：添加完整的Jackson配置

### 3. 实体类缺少注解
- **问题**：BaseEntity中的时间字段缺少@JsonFormat注解
- **影响**：时间字段使用默认序列化方式（时间戳）
- **解决**：为所有时间字段添加格式化注解

## 修复方案

### 1. 移除Jackson版本冲突
```xml
<!-- 移除以下配置 -->
<jackson.version>2.9.10</jackson.version>
```

### 2. 完善ApplicationConfig配置
```java
@Bean
public Jackson2ObjectMapperBuilderCustomizer jacksonObjectMapperCustomization() {
    return builder -> {
        // 设置时区为UTC
        builder.timeZone(TimeZone.getTimeZone("UTC"));
        
        // 设置日期格式，与Nacos配置保持一致
        builder.dateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
        
        // 禁用将日期写为时间戳
        builder.featuresToDisable(
            com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS
        );
        
        // Long类型序列化为字符串，避免前端精度丢失
        builder.serializerByType(Long.class, ToStringSerializer.instance);
        builder.serializerByType(Long.TYPE, ToStringSerializer.instance);
    };
}
```

### 3. 为BaseEntity添加时间格式化注解
```java
@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
@TableField(fill = FieldFill.INSERT)
private Date F_CRTM;

@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
@TableField(fill = FieldFill.INSERT_UPDATE)
private Date F_EDTM;
```

## 配置说明

### 时间格式
- **格式**：`yyyy-MM-dd'T'HH:mm:ss.SSS'Z'`
- **示例**：`2024-01-15T10:30:45.123Z`
- **说明**：ISO 8601标准格式，与前端兼容性好

### 时区设置
- **时区**：UTC
- **原因**：统一时区，避免时区转换问题
- **配置位置**：
  1. 启动类：`TimeZone.setDefault(TimeZone.getTimeZone("UTC"))`
  2. Jackson配置：`builder.timeZone(TimeZone.getTimeZone("UTC"))`
  3. 注解配置：`timezone = "UTC"`

## 验证方法

### 1. 重新编译项目
```bash
mvn clean compile
```

### 2. 启动服务
```bash
mvn spring-boot:run
```

### 3. 测试接口
调用任何返回时间字段的接口，检查返回格式：

**修复前（错误）**：
```json
{
  "F_CRTM": 1705312245123,
  "F_EDTM": 1705312245123
}
```

**修复后（正确）**：
```json
{
  "F_CRTM": "2024-01-15T10:30:45.123Z",
  "F_EDTM": "2024-01-15T10:30:45.123Z"
}
```

## 注意事项

1. **版本兼容性**：确保不要手动指定Jackson版本，让Spring Boot管理
2. **时区一致性**：所有时间相关配置都使用UTC时区
3. **格式统一性**：所有时间字段都使用相同的格式模式
4. **前端适配**：前端需要能够解析ISO 8601格式的时间字符串

## 相关文件

- `pom.xml` - 移除Jackson版本定义
- `ApplicationConfig.java` - Jackson全局配置
- `BaseEntity.java` - 实体类时间字段注解
- `YingFeiSystemApplication.java` - 时区设置

## 测试建议

建议测试以下接口确认修复效果：
1. `/oper_log_inf/list` - 操作日志列表
2. 任何包含创建时间、更新时间的查询接口
3. 新增/更新操作后的返回值
